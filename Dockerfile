# build stage
FROM golang:alpine AS builder
#RUN apk --no-cache add build-base git bzr mercurial gcc
RUN go version

WORKDIR /src
RUN echo $GOPATH
COPY go.mod go.sum ./
# COPY go.mod .go/pkg* /go/pkg/
# RUN ls -al /go/pkg
RUN GO111MODULE=on go mod download
COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o app-crm server.go
# RUN go build -o app-crm server.go

#final stage
FROM alpine

# install openssl
RUN apk add --update openssl && \
    rm -rf /var/cache/apk/*

RUN apk add --no-cache ca-certificates

#RUN openssl genrsa -out config/ssl/server.key 2048
#RUN openssl ecparam -genkey -name secp384r1 -out config/ssl/server.key
#RUN openssl req -new -x509 -sha256 -key config/ssl/server.key -out config/ssl/server.pem -days 90 \
#    --subj "/C=ID/ST=D.I.Y Yogyakarta/L=Yogyakarta/O=UNIQ Inc./OU=IT/CN=www.uniq.id"

COPY config/ /config
COPY *.env *.yml /
COPY --from=builder src/app-crm ./

ENTRYPOINT ["./app-crm"]
