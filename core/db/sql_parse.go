package db

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"os"
	"reflect"
	"runtime"
	"strings"
	"sync"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
)

func QueryArrayGo(wg *sync.WaitGroup, result chan []map[string]interface{}, sqlQuery string, args ...interface{}) {
	defer wg.Done()
	data, err := QueryArray(sqlQuery, args...)
	log.IfError(err)
	result <- data
}

func QueryGo(wg *sync.WaitGroup, result chan map[string]interface{}, sqlQuery string, args ...interface{}) {
	defer wg.Done()
	data, err := Query(sqlQuery, args...)
	utils.CheckErr(err)
	result <- data
}

func QueryArrayContext(ctx context.Context, sqlQuery string, args ...interface{}) ([]map[string]interface{}, error) {
	timeStart := time.Now()
	db := GetConn()
	tableData := make([]map[string]interface{}, 0)

	//stmt, err := db.Prepare(sqlQuery)
	//if err != nil {
	//	return tableData, err
	//}
	//defer stmt.Close()
	//
	//rows, err := stmt.Query(args...)
	//start := time.Now()
	rows, err := db.QueryContext(ctx, sqlQuery, args...)

	if log.IfError(err) {
		// fmt.Println("\n>> query err: ", err)
		fmt.Println(">>", strings.Join(strings.Fields(getSQLRaw(sqlQuery, args...)), " "))
		return tableData, err
	}

	// logSQLRaw(sqlQuery, args...)

	//for i := 0; i < len(args); i++{
	//	index := strings.Index(sqlQuery, "?")
	//	sqlQuery = sqlQuery[:index] + utils.ToString(args[i]) + sqlQuery[index+1:]
	//}
	//fmt.Println(sqlQuery)
	if os.Getenv("server") == "localhost" {
		logSQLRaw(sqlQuery, args...)
	}

	//fmt.Println(sqlQuery)

	//elapsed := time.Since(start)
	//fmt.Println("--- Query (array) took : ", elapsed)

	defer func() {
		log.IfError(rows.Close())
	}()

	// defer rows.Close()

	columns, err := rows.Columns()
	if err != nil {
		return tableData, nil
	}

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	for rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			return tableData, nil
		}

		entry := make(map[string]interface{})
		for i, col := range columns {
			v := values[i]

			b, ok := v.([]byte)
			if ok {
				entry[col] = string(b)
			} else {
				entry[col] = v
			}
		}
		tableData = append(tableData, entry)
	}

	if time.Since(timeStart).Seconds() > 15 {
		_, file, line, _ := runtime.Caller(1)
		log.IfError(fmt.Errorf("%v:%v query took %v \n ``` %v ```", file, line, time.Since(timeStart), getSQLRaw(sqlQuery, args...)))
	}
	return tableData, nil
}

func QueryArray(sqlQuery string, args ...interface{}) ([]map[string]interface{}, error) {
	context, cancel := context.WithTimeout(context.Background(), time.Duration(time.Second*70))
	defer cancel()
	return QueryArrayContext(context, sqlQuery, args...)
}

func Query(sqlQuery string, args ...interface{}) (map[string]interface{}, error) {
	db := GetConn()
	entryData := make(map[string]interface{}, 0)

	//stmt, err := db.Prepare(sqlQuery)
	//if err != nil {
	//	return entryData, err
	//}
	//defer stmt.Close()
	//
	//rows, err := stmt.Query(args...)

	//start := time.Now()
	rows, err := db.Query(sqlQuery, args...)
	if err != nil {
		// logSQLRaw(sqlQuery, args...)
		fmt.Println(">>", strings.Join(strings.Fields(getSQLRaw(sqlQuery, args...)), " "))
		return entryData, err
	}

	// logSQLRaw(sqlQuery, args...)
	//elapsed := time.Since(start)
	//fmt.Println("-- Query took : ", elapsed)

	defer func() {
		log.IfError(rows.Close())
	}()

	//for i := 0; i < len(args); i++{
	//	index := strings.Index(sqlQuery, "?")
	//	sqlQuery = sqlQuery[:index] + args[i].(string) + sqlQuery[index+1:]
	//}
	//fmt.Println(sqlQuery)

	columns, err := rows.Columns()
	if err != nil {
		return entryData, nil
	}

	count := len(columns)
	values := make([]interface{}, count)
	scanArgs := make([]interface{}, count)

	for i := range values {
		scanArgs[i] = &values[i]
	}

	if rows.Next() {
		err := rows.Scan(scanArgs...)
		if err != nil {
			return entryData, nil
		}

		for i, col := range columns {
			v := values[i]

			b, ok := v.([]byte)
			if ok {
				entryData[col] = string(b)
			} else {
				entryData[col] = v
			}
		}
	}

	return entryData, nil
}

func insertQuery(table string, data map[string]interface{}) (string, []interface{}) {
	values := make([]interface{}, 0)
	query := "INSERT INTO " + table + " ("
	for col, val := range data {
		query += col + ","
		values = append(values, val)
	}
	query = query[:len(query)-1] + ") VALUES (" + strings.Repeat("?, ", len(data)-1) + "?)"
	return query, values
}

func insertBulkQuery(table string, data []map[string]interface{}) (string, []interface{}) {
	if len(data) == 0 {
		fmt.Println("bulkInsert: no data given")
		return "", nil
	}

	columns := make([]string, 0)
	valueArgs := make([]interface{}, 0)
	valueStrings := make([]string, 0)

	for i, rows := range data {
		if i == 0 {
			for k := range rows {
				columns = append(columns, k)
			}
		}

		for _, col := range columns {
			valueArgs = append(valueArgs, rows[col])
		}

		valueStrings = append(valueStrings, WhereIn(len(rows)))
	}
	stmt := fmt.Sprintf("INSERT INTO "+table+" ("+strings.Join(columns, ",")+") VALUES %s",
		strings.Join(valueStrings, ","))
	return stmt, valueArgs
}

func updateQuery(table string, data map[string]interface{}, whereCond string, whereParams ...interface{}) (string, []interface{}) {
	values := make([]interface{}, 0)
	query := "UPDATE " + table + " SET "
	for col, val := range data {
		if reflect.ValueOf(val).Kind() == reflect.Map {
			v := reflect.ValueOf(val)
			for _, key := range v.MapKeys() {
				query += fmt.Sprintf("%s = %s %s ?", col, col, key.String())
				values = append(values, v.MapIndex(key).Interface())
			}
		} else {
			query += col + " = ?,"
			values = append(values, val)
		}
	}

	query = strings.TrimRight(query, ",") + " WHERE " + whereCond

	values = append(values, whereParams...)
	return query, values
}

func deleteQuery(table string, whereCond string, whereParams ...interface{}) string {
	query := "DELETE FROM " + table + " WHERE " + whereCond
	return query
}

func Insert(table string, data map[string]interface{}) (sql.Result, error) {
	query, values := insertQuery(table, data)
	res, err := GetConn().Exec(query, values...)
	if err != nil {
		for i := range query {
			if strings.Contains(query, "?") {
				val := utils.ToString(values[i])
				query = strings.Replace(query, "?", val, 1)
			} else {
				break
			}
		}
		_, file, no, _ := runtime.Caller(1)
		log.Info("%s:%d insert err : %v \n'%s'", file, no, err, query)
	}

	return res, err
}

func BulkInsert(table string, data []map[string]interface{}) (sql.Result, error) {
	stmt, valueArgs := insertBulkQuery(table, data)
	// if os.Getenv("ENV") == "localhost" {
	// 	fmt.Println("query: ", stmt)
	// }
	resp, err := db.Exec(stmt, valueArgs...)
	if err != nil {
		_, file, line, _ := runtime.Caller(1)
		fmt.Printf("%v:%v >> Query Err: %v", file, line, stmt)
	}

	return resp, err
}

func Update(table string, data map[string]interface{}, whereCond string, whereParams ...interface{}) (sql.Result, error) {
	query, values := updateQuery(table, data, whereCond, whereParams...)
	//query = query[:len(query)-4]
	logSQLRaw(query, values...)

	res, err := GetConn().Exec(query, values...)
	if err != nil {
		if err != nil {
			_, file, line, _ := runtime.Caller(1)
			fmt.Printf("%v:%v >> Query Err: %v", file, line, query)
		}
	}

	return res, err
}

func Delete(table string, whereCond string, whereParams ...interface{}) (sql.Result, error) {
	if whereCond == "" || len(whereParams) == 0 {
		return nil, errors.New("can not delete without where condition")
	}

	query := deleteQuery(table, whereCond, whereParams...)
	res, err := GetConn().Exec(query, whereParams...)
	if err != nil {
		fmt.Println("Executing Delete Query Error : ", getSQLRaw(query, whereParams...))
		_, file, line, _ := runtime.Caller(1)
		fmt.Printf("%v:%v >> Query Err: %v", file, line, query)
	}

	return res, err
}

func logSQLRaw(sql string, params ...interface{}) {
	for i := 0; i < len(params); i++ {
		index := strings.Index(sql, "?")
		sql = sql[:index] + utils.ToString(params[i]) + sql[index+1:]
		//fmt.Println("end -> ", sql[index+1:])
	}
	fmt.Println("\n-->", strings.TrimSpace(sql))
}

func getSQLRaw(sql string, params ...interface{}) string {
	for i := 0; i < len(params); i++ {
		index := strings.Index(sql, "?")
		if index < 0 {
			continue
		}
		sql = sql[:index] + utils.ToString(params[i]) + sql[index+1:]
	}
	return sql
}
