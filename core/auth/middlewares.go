package auth

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/dgrijalva/jwt-go"
	"github.com/dgrijalva/jwt-go/request"
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
)

var env = os.Getenv("ENV")

func ValidateToken(next fasthttp.RequestHandler) fasthttp.RequestHandler {
	return fasthttp.RequestHandler(func(ctx *fasthttp.RequestCtx) {
		fmt.Printf("[REQ-STR] %s  %s -- %s \n", log.GetDate(), ctx.Method(), ctx.URI().Path())
		auth := InitJWTAuth()
		timeStart := time.Now()

		authToken := ctx.Request.Header.Peek("Authorization")
		req := new(http.Request)
		req.Header = http.Header{}
		req.Header.Set("Authorization", string(authToken))
		token, err := request.ParseFromRequest(req, request.OAuth2Extractor, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
				return nil, &InvalidSigningMethod{Algorithm: cast.ToString(token.Header["alg"])}
			} else {
				return auth.PublicKey, nil
			}
		})

		isValidToken := false
		if err == nil && token.Valid {
			claims := token.Claims.(jwt.MapClaims)
			for key, data := range claims {
				if key == "uid" || key == "mid" {
					ctx.Request.Header.Set(key, utils.Decrypt(utils.ToString(data), utils.UID_PASSSERVER))
				} else {
					ctx.Request.Header.Set(key, utils.ToString(data))
				}
			}
			isValidToken = true
		}

		//maybe: this is to handle request from api-gateway (which using different jwt token)
		if e, ok := err.(*jwt.ValidationError); ok {
			if _, ok2 := e.Inner.(*InvalidSigningMethod); ok2 {
				claim, err := ParseJwToken(string(authToken))
				for key, data := range claim {
					ctx.Request.Header.Set(key, utils.ToString(data))
				}
				isValidToken = err == nil
			}
		}

		if isValidToken {
			if os.Getenv("ENV") == "development" {
				origin := string(ctx.Request.Header.Peek("Origin"))
				if origin == "https://app-crm.uniq-dev.xyz" || origin == "https://order.uniq-dev.xyz" {
					ctx.Request.Header.Set("uid", "1")
				}
			}
			ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
			ctx.Response.Header.Set("X-Frame-Options", "DENY")
			ctx.SetContentType("application/json")

			//date := time.Unix(time.Now().Unix()+25200, 0).Format("2006-01-02 15:04:05")
			//fmt.Println("[request]  ", date, "  ", string(ctx.Method()), "  ", string(ctx.URI().Path()))
			timeStart = time.Now()
			next(ctx)
			// fmt.Printf("[REQ-END] %s  %s -- %s  %d  %v \n", log.GetDate(), ctx.Method(), ctx.URI().Path(), ctx.Response.StatusCode(), time.Since(timeStart))
			// if time.Since(timeStart).Minutes() > 1.0 {
			// 	log.IfError(fmt.Errorf("request took %v - %s (%s)", time.Since(timeStart), ctx.URI().String(), ctx.Method()))
			// }
			logRequest(ctx, timeStart)
			return
		}

		fmt.Println("err", err)
		log.Warn("Authorize failed. token invalid or expired : %s", string(authToken))
		logRequest(ctx, timeStart)
		ctx.SetStatusCode(fasthttp.StatusUnauthorized)
	})
}

func ParseJwToken(authToken string) (map[string]interface{}, error) {
	tokenData := strings.Split(authToken, ".")
	if len(tokenData) != 3 {
		return nil, fmt.Errorf("invalid token")
	}

	//get token data
	tokenBody := tokenData[1] //jwt's payload

	//decode token
	decoded, _ := base64.RawStdEncoding.DecodeString(tokenBody)
	var authData AuthTokenData
	err := json.Unmarshal(decoded, &authData)
	if err != nil {
		return nil, err
	}

	userSession := map[string]interface{}{
		"uid": authData.Data.User.BusinessID,
	}
	return userSession, nil
}

func ValidatePublicKey(next fasthttp.RequestHandler) fasthttp.RequestHandler {
	return func(ctx *fasthttp.RequestCtx) {
		// fmt.Printf("[REQ-STR] %s  %s -- %s \n", log.GetDate(), ctx.Method(), ctx.URI().Path())
		adminId := ctx.Request.Header.Peek("Public-Key")
		if adminId == nil || string(adminId) == "" {
			log.Warn("public-key is not defined")
			ctx.SetStatusCode(fasthttp.StatusForbidden)
		} else {
			//if user sent authorization code, check it
			isValid := claimToken(ctx, "public")
			if !isValid {
				fmt.Println("invlid public-key", adminId)
				ctx.SetStatusCode(fasthttp.StatusForbidden)
				return
			}
			ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
			ctx.Response.Header.Set("X-Frame-Options", "DENY")
			ctx.SetContentType("application/json")
			// fmt.Println("header: ", ctx.Request.Header.String())

			timeStart := time.Now()
			next(ctx)
			// fmt.Printf("[REQ-END] %s  %s -- %s  %d  %v \n", log.GetDate(), ctx.Method(), ctx.URI().Path(), ctx.Response.StatusCode(), time.Since(timeStart).Milliseconds())
			logRequest(ctx, timeStart)
		}
	}
}

func ValidateDeveloperToken(next fasthttp.RequestHandler) fasthttp.RequestHandler {
	return func(ctx *fasthttp.RequestCtx) {
		isValid := claimToken(ctx, "private")
		if !isValid {
			fmt.Println("invalid developer key")
			ctx.SetStatusCode(fasthttp.StatusForbidden)
			return
		}
		ctx.Response.Header.Set("X-Content-Type-Options", "nosniff")
		ctx.Response.Header.Set("X-Frame-Options", "DENY")
		ctx.SetContentType("application/json")

		timeStart := time.Now()
		next(ctx)
		logRequest(ctx, timeStart)
	}
}

func claimToken(ctx *fasthttp.RequestCtx, reqType string) bool {
	authToken := ctx.Request.Header.Peek("Authorization")
	isValidAuth := false
	if authToken != nil {
		auth := InitJWTAuth()
		req := new(http.Request)
		req.Header = http.Header{}
		req.Header.Set("Authorization", string(authToken))
		token, err := request.ParseFromRequest(req, request.OAuth2Extractor, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			} else {
				return auth.PublicKey, nil
			}
		})
		if err == nil && token.Valid {
			isValidAuth = true
			claims := token.Claims.(jwt.MapClaims)
			for key, data := range claims {
				if key == "uid" || key == "mid" {
					ctx.Request.Header.Set(key, utils.Decrypt(utils.ToString(data), utils.UID_PASSSERVER))
				} else {
					ctx.Request.Header.Set(key, utils.ToString(data))
				}
			}
		}

		//maybe: this is to handle request from api-gateway (which using different jwt token)
		//TODO: this has to be validated, the request should come from api-gateway only
		if e, ok := err.(*jwt.ValidationError); ok {
			if _, ok2 := e.Inner.(*InvalidSigningMethod); ok2 {
				claim, err := ParseJwToken(string(authToken))
				for key, data := range claim {
					ctx.Request.Header.Set(key, utils.ToString(data))
				}
				isValidAuth = err == nil
			}
		}
	}

	if reqType == "private" && !isValidAuth {
		return false
	}

	businessId := utils.Decrypt(string(ctx.Request.Header.Peek("Public-Key")), utils.UID_PUBKEY)
	if utils.IsNumber(businessId) {
		ctx.Request.Header.Set("uid", businessId)
	} else if reqType == "public" {
		return false
	}

	// fmt.Printf("host>> '%s' \n", string(ctx.Request.URI().Host()))
	if os.Getenv("ENV") == "development" {
		// host := string(ctx.Request.URI().Host())
		origin := string(ctx.Request.Header.Peek("Origin"))
		// referer := string(ctx.Request.Header.Peek("Referer"))
		// fmt.Printf("host: '%s', origin: '%s', referer: '%s'\n", host, origin, referer)

		if origin == "https://app-crm.uniq-dev.xyz" || origin == "https://order.uniq-dev.xyz" {
			ctx.Request.Header.Set("uid", "1")
		}
	}

	return true
}

func logRequest(ctx *fasthttp.RequestCtx, timeStart time.Time) {
	logRequest := map[string]interface{}{
		"date":            log.GetDate(),
		"method":          string(ctx.Method()),
		"request":         string(ctx.URI().Path()),
		"code":            ctx.Response.StatusCode(),
		"time_elapsed":    time.Since(timeStart).Seconds(),
		"platform":        string(ctx.Request.Header.Peek("Platform")),
		"real_ip":         string(ctx.Request.Header.Peek("X-Forwarded-For")), //X-Forwarded-For or Cf-Connecting-Ip
		"real_ip_address": string(ctx.Request.Header.Peek("Cf-Connecting-Ip")),
	}
	fmt.Println(utils.SimplyToJson(logRequest))

	if time.Since(timeStart).Seconds() > 15 {
		if env == "development" {
			log.Info("request took %v - %s (%s)", time.Since(timeStart), ctx.URI().String(), ctx.Method())
		} else {
			log.IfError(fmt.Errorf("request took %v - %s (%s) \n %s", time.Since(timeStart), ctx.URI().String(), ctx.Method(), utils.SimplyToJson(logRequest)))
		}
	}
}

type middleware func(fasthttp.RequestHandler) fasthttp.RequestHandler

func MultiMiddleware(next fasthttp.RequestHandler, m ...middleware) fasthttp.RequestHandler {
	if len(m) == 0 {
		return next
	}

	return m[0](MultiMiddleware(next, m[1:cap(m)]...))
}

var (
	corsAllowHeaders     = "Authorization, Public-Key, Accept, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, browsername, platform, user-agent, UserAgent, isPhysicalDevice, Version, Brand, Manufacture, Model"
	corsAllowMethods     = "HEAD,GET,POST,PUT,DELETE,OPTIONS"
	corsAllowOrigin      = "*"
	corsAllowCredentials = "true"
)

func CORS(next fasthttp.RequestHandler) fasthttp.RequestHandler {
	return func(ctx *fasthttp.RequestCtx) {
		// fmt.Println("cors: ", string(ctx.Request.URI().FullURI()))
		if string(ctx.Method()) == fasthttp.MethodOptions {
			ctx.Response.Header.Set("Access-Control-Allow-Credentials", corsAllowCredentials)
			ctx.Response.Header.Set("Access-Control-Allow-Headers", corsAllowHeaders)
			ctx.Response.Header.Set("Access-Control-Allow-Methods", corsAllowMethods)
			ctx.Response.Header.Set("Access-Control-Allow-Origin", corsAllowOrigin)
			ctx.SetStatusCode(fasthttp.StatusNoContent)
			return
		}

		ctx.Response.Header.Set("Access-Control-Allow-Credentials", corsAllowCredentials)
		ctx.Response.Header.Set("Access-Control-Allow-Origin", corsAllowOrigin)
		// fmt.Println("allowing cors...")
		// fmt.Println("CORS - headers resp : ", ctx.Response.Header.String(), "headers req : ", ctx.Request.Header.String())
		next(ctx)
	}
}

func EnableCors(ctx *fasthttp.RequestCtx) {
	ctx.Response.Header.Set("Access-Control-Allow-Credentials", corsAllowCredentials)
	ctx.Response.Header.Set("Access-Control-Allow-Headers", corsAllowHeaders)
	ctx.Response.Header.Set("Access-Control-Allow-Methods", corsAllowMethods)
	ctx.Response.Header.Set("Access-Control-Allow-Origin", corsAllowOrigin)
	//fmt.Println("enable cors for ", ctx.URI().String(), "headers resp : ", ctx.Response.Header.String(), "headers req : ", ctx.Request.Header.String())
	log.Info("enable cors for accessing: %s, origin: %s", ctx.URI().String(), ctx.Request.Header.Peek("Origin"))
	ctx.SetStatusCode(fasthttp.StatusNoContent)
}
