package job

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/google"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/messaging"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/promotion"
	"gitlab.com/uniqdev/backend/api-membership/module/transaction"
)

type pubSubSubscriber struct {
	promotionUc   promotion.UseCase
	transactionUc transaction.UseCase
}

func RunPubSubSubscription(promotion promotion.UseCase, transaction transaction.UseCase) {
	sub := pubSubSubscriber{promotion, transaction}
	env := os.Getenv("ENV")

	if "false" == os.Getenv("ENABLE_PUBSUB") {
		log.Info("---- PubSub Disabled ----")
		return
	}

	subsId := fmt.Sprintf("refresh_member_level_%s_crm", env)
	go google.Subscribe(subsId, sub.refreshMemberLevel)

	subsId = fmt.Sprintf("crm-feedback-%s-api-crm", env)
	go google.Subscribe(subsId, func(data []byte) bool {
		return sub.receiveFeedback(data)
	})

	subsId = fmt.Sprintf("")
}

func (s *pubSubSubscriber) receiveFeedback(data []byte) bool {
	var feedbackPubsub struct {
		SalesFkid string `json:"sales_fkid,omitempty"`
	}

	if log.IfError(json.Unmarshal(data, &feedbackPubsub)) {
		return false
	}
	return s.transactionUc.ReceiveFeedback(feedbackPubsub.SalesFkid) == nil
}

func (s *pubSubSubscriber) refreshMemberLevel(data []byte) bool {
	// var pubsub models.PubSub
	// log.IfError(json.Unmarshal(data, &pubsub))

	// pubsubData, err := base64.StdEncoding.DecodeString(pubsub.Message.Data)
	// log.IfError(err)
	// fmt.Println("receive from pubsub data : ", string(pubsubData))

	var memberLevelPubsub struct {
		MemberId interface{} `json:"member_id,omitempty"`
		AdminId  string      `json:"admin_id,omitempty"`
	}

	err := json.Unmarshal(data, &memberLevelPubsub)
	log.IfError(err)
	s.promotionUc.RunPromotionRole(domain.PromotionRoleParam{AdminId: cast.ToInt(memberLevelPubsub.AdminId)})

	return true
}

func PubSubCheckMemberLifeTime(ctx *fasthttp.RequestCtx) {
	var pubsub models.PubSub
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&pubsub)
	if err != nil {
		fmt.Fprintf(os.Stderr, "decode json error - %v", err)
	}

	data, err := base64.StdEncoding.DecodeString(pubsub.Message.Data)
	fmt.Println("[check lifetime] receive from pubsub data : ", string(data))
	if string(data) != "" {
		go CheckMemberLifeTime()
	}

	ctx.SetStatusCode(fasthttp.StatusCreated)
}

func CheckMemberLifeTime() {
	log.Info("[job running] CheckMemberLifeTime")
	sql := `
select md.member_fkid, md.admin_fkid,
       p.name as level_name, md.type_fkid,
       m.name,
       max(md.firebase_token) as firebase_token,
       max(mth.data_created)  as level_start,
       mt.lifetime_day,
       unix_timestamp(
               DATE_ADD(from_unixtime(floor(coalesce(sales.last_transaction, max(mth.data_created)) / 1000)), INTERVAL
                        mt.lifetime_day DAY)) *
       1000                   as level_expired,
       DATEDIFF(DATE_ADD(from_unixtime(floor(coalesce(sales.last_transaction, max(mth.data_created)) / 1000)), INTERVAL
                         mt.lifetime_day DAY),
                NOW())        as level_remain_old,
		DATEDIFF(DATE_ADD(from_unixtime(floor(if(sales.last_transaction > max(mth.data_created), sales.last_transaction,  max(mth.data_created)) / 1000)), INTERVAL
                         mt.lifetime_day DAY), NOW()) as level_remain
from members_detail md
         join members m on md.member_fkid = m.member_id
         join members_type mt
              on md.type_fkid = mt.type_id
         join products p on mt.product_fkid = p.product_id
         join members_type_history mth on mt.type_id = mth.member_type_fkid
         left join (
    select member_fkid, admin_fkid, max(s.time_created) as last_transaction
    from sales s
             join outlets o on s.outlet_fkid = o.outlet_id
    where member_fkid is not null
      and status = 'success'
    group by member_fkid, o.admin_fkid
) sales on sales.member_fkid = md.member_fkid and sales.admin_fkid = md.admin_fkid
group by mth.member_type_fkid, md.member_fkid, md.admin_fkid `

	dataArr, err := db.QueryArray(sql)
	if log.IfError(err) {
		return
	}

	log.Info("data member to check : %d \ndata --> %s", len(dataArr), utils.SimplyToJson(dataArr))
	for _, data := range dataArr {
		levelRemain := utils.ToInt(data["level_remain"])
		if levelRemain <= 0 {
			//downgrade member
			downgradeMember(data["admin_fkid"], data["member_fkid"], data["lifetime_day"])
		} else if levelRemain == 1 || levelRemain == 7 {
			//give them warning
			title := "LEVEL REMINDER"
			msg := fmt.Sprintf("Waktu kamu tersisa %d hari lagi agar tetap berada di level %s. "+
				"Ayo terus lakukan transaksi agar kamu tidak turun level", utils.ToInt(data["level_remain"]), data["level_name"])
			messaging.PushNotification(messaging.Notification{
				Title:         title,
				Message:       msg,
				MemberId:      data["member_fkid"],
				AdminId:       data["admin_fkid"],
				FirebaseToken: utils.ToString(data["firebase_token"]),
			})
			log.Info(msg)
		}
	}
}

func downgradeMember(adminId, memberId, lifeTimeDay interface{}) {
	sql := `select sum(point_lost) as point_lost, sum(spend_lost) as spend_lost
from members_type_history
where member_fkid = ?
  and admin_fkid = ?`

	dataLost, err := db.Query(sql, memberId, adminId)
	log.IfError(err)

	//totalLost := utils.ToInt(dataLost["point_lost"]) + utils.ToInt(dataLost["spend_lost"])

	//get the lower level, comparing to current level
	sql = `
select (point_target + spent_target) as target, p.name, mt.type_id, point_target, spent_target as spend_target 
from members_type mt
         join products p on mt.product_fkid = p.product_id
where p.data_status = 'on'
  and mt.admin_fkid = ?
  and (point_target + spent_target) < (select (point_target + spent_target)
                                       from members_type mt
                                                join members_detail md on mt.type_id = md.type_fkid
                                       where md.member_fkid = ?
                                         and md.admin_fkid = ?)
order by target desc
limit 1 `
	memberType, err := db.Query(sql, adminId, memberId, adminId)
	if log.IfError(err) {
		return
	}

	if len(memberType) == 0 {
		// log.Info("can not downgraded member %s (%s). maybe current level is basic", memberId, adminId)
		return
	}

	log.Info("member %s (%s) should downgraded to %d \ndata -> %s", memberId, adminId, memberType["type_id"], utils.SimplyToJson(memberType))

	_, err = db.Update("members_detail", map[string]interface{}{
		"type_fkid": memberType["type_id"],
	}, "member_fkid=? and admin_fkid=?", memberId, adminId)
	if log.IfError(err) {
		return
	}

	//get total point and spend, despite if its being used to buy promotion, etc
	//that's why, we should take the data from sales (in which that is the way member get the point & spend)
	sql = `
select coalesce(sum(grand_total), 0) as total_spend, coalesce(sum(point_earned), 0) as total_point,  min(md.total_point) as current_point
from members_detail md
         join members m on md.member_fkid = m.member_id
         left join (select grand_total, member_fkid, point_earned
                    from sales s
                             join outlets o on s.outlet_fkid = o.outlet_id
                    where status = 'success'
						 and (payment not like '%duty meal%' or payment not like '%compliment%') 
                      and o.admin_fkid = ? ) sales on sales.member_fkid = md.member_fkid
where md.member_fkid = ?
  and md.admin_fkid = ? `

	member, err := db.Query(sql, adminId, memberId, adminId)
	log.IfError(err)

	pointLost := utils.ToInt(member["total_point"]) - utils.ToInt(dataLost["point_lost"]) - utils.ToInt(memberType["point_target"])
	spendLost := utils.ToInt(member["total_spend"]) - utils.ToInt(dataLost["spend_lost"]) - utils.ToInt(memberType["spend_target"])
	currentPoint := utils.ToInt(member["total_point"]) - utils.ToInt(dataLost["point_lost"])
	currentSpend := utils.ToInt(member["total_spend"]) - utils.ToInt(dataLost["spend_lost"])

	log.Info("point lost : %d -> %s (total point) - %s (previous lost) - %d (target)", pointLost, member["total_point"], dataLost["point_lost"], memberType["point_target"])
	log.Info("spend lost : %d -> %s (total spend) - %s (previous lost) - %d (target)", spendLost, member["total_spend"], dataLost["spend_lost"], memberType["spend_target"])
	log.Info("current point : %d - current spend : %d", currentPoint, currentSpend)

	//if result from lost calculation is minus, no need to set lost
	if pointLost < 0 {
		pointLost = 0
		log.Info("reset point lost to 0")
	}

	if spendLost < 0 {
		spendLost = 0
		log.Info("reset spend lost to 0")
	}

	_, err = db.Insert("members_type_history", map[string]interface{}{
		"member_type_fkid":  memberType["type_id"],
		"member_fkid":       memberId,
		"admin_fkid":        adminId,
		"point":             currentPoint,
		"spend":             currentSpend,
		"point_lost":        pointLost,
		"spend_lost":        spendLost,
		"member_type_point": memberType["point_target"],
		"member_type_spend": memberType["spend_target"],
		"data_created":      time.Now().Unix() * 1000,
		"change_log":        "lifetime",
		"change_type":       "down",
	})
	log.IfError(err)

	title := "LEVEL DOWNGRADED"
	message := fmt.Sprintf("Oops! kamu harus turun level ke %s, karena sudah %d hari kamu tidak melakukan transaksi", strings.ToUpper(utils.ToString(memberType["name"])), utils.ToInt(lifeTimeDay))
	messaging.PushNotification(messaging.Notification{
		Title:    title,
		Message:  message,
		MemberId: memberId,
		AdminId:  adminId,
	})
}
