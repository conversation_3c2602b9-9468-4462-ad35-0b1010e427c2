package job

import (
	"os"
	"time"

	"github.com/go-co-op/gocron"
	"github.com/joho/godotenv"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/module/product"
	"gitlab.com/uniqdev/backend/api-membership/module/promotion"
	"gitlab.com/uniqdev/backend/api-membership/module/transaction"
)

func RunCronJob(promotionUc promotion.UseCase, productUc product.UseCase, transUc transaction.UseCase) {
	_ = godotenv.Load()
	if os.Getenv("disable_cron") == "true" {
		log.Info("cron is disable")
		return
	}

	cron := gocron.NewScheduler(time.UTC)

	cron.Every(1).Day().At("05:30").Do(CheckMemberLifeTime)
	cron.Every(1).Day().At("20:00").Do(RemoveTransactionFromFirebaseDb)
	// gocron.Every(1).Day().At("20:00").Do(ResetMemberPoint)

	cron.Every(1).Day().At("02:00").Do(promotionUc.RunPromotionRole, domain.PromotionRoleParam{})
	cron.Every(1).Day().At("02:10").Do(promotionUc.RunPromotionReminder, 7) //for testing
	cron.Every(1).Day().At("02:10").Do(promotionUc.RunPromotionReminder, 5)
	cron.Every(1).Day().At("02:30").Do(promotionUc.RunPromotionReminder, 1)

	// cron.Every(1).Day().At("01:00").Do(promotionUc.SendNotifyPromotion)
	cron.Every(3).Hours().Do(productUc.RunNotifyProduct)
	cron.Every(10).Minutes().Do(checkingExpiredTransaction)

	//transaction - set sentiment
	cron.Every(1).Day().At("21:00").Do(transUc.SetSentimentFeedback)
	cron.Every(1).Monday().At("01:40").Do(transUc.SendFeedbackReport)

	if env := os.Getenv("ENV"); env == "development" {
		cron.Every(3).Minutes().Do(transUc.RunPaymentTimeout)
	}

	log.Info("cron is scheduled..")
	cron.StartAsync()
}
