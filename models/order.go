package models

import (
	"encoding/json"
	"fmt"
	"runtime"
)

type Order struct {
	IDOrder    string `json:"id_order,omitempty"`
	OutletId   int    `json:"outlet_id,omitempty"`
	PickupTime int64  `json:"pickup_time,omitempty"`
	GrandTotal int    `json:"grand_total,omitempty"`
	OrderList  []struct {
		Price    int `json:"price,omitempty"`
		Qty      int `json:"qty,omitempty"`
		SubTotal int `json:"sub_total,omitempty"`
		Taxes    []struct {
			Category string `json:"category,omitempty"`
			ID       string `json:"id,omitempty"`
			Name     string `json:"name,omitempty"`
			Total    int    `json:"total,omitempty"`
			Type     string `json:"type,omitempty"`
			Value    int    `json:"value,omitempty"`
		} `json:"taxes,omitempty"`
		ProductDetailID int    `json:"product_detail_id,omitempty"`
		ProductID       int    `json:"product_id,omitempty"`
		Name            string `json:"name,omitempty"`
		Note            string `json:"note,omitempty"`
	} `json:"order_list,omitempty"`
	Taxes []struct {
		Category string `json:"category,omitempty"`
		ID       string `json:"id,omitempty"`
		Name     string `json:"name,omitempty"`
		Total    int    `json:"total,omitempty"`
		Type     string `json:"type,omitempty"`
		Value    int    `json:"value,omitempty"`
	} `json:"taxes,omitempty"`
	Payments []struct {
		Method        string `json:"method,omitempty"`
		Total         int    `json:"total,omitempty"`
		TransactionId string `json:"transaction_id,omitempty"`
	} `json:"payments,omitempty"`
	CustomerName    string `json:"customer_name,omitempty"`
	ReceiptReceiver string `json:"receipt_receiver,omitempty"`
	DiningTable     string `json:"dining_table,omitempty"`
}

type OrderConfigEntity struct {
	Config      string `json:"config,omitempty"`
	Value       string `json:"value,omitempty"`
	AdminID     int    `json:"admin_id,omitempty"`
	TimeUpdated int64  `json:"time_updated,omitempty"`
}

func (o OrderConfigEntity) ToConfigTransaction() ConfigTransaction {
	var result ConfigTransaction
	if o.Config == "transaction" {
		err := json.Unmarshal([]byte(o.Value), &result)
		if err != nil {
			_, file, line, _ := runtime.Caller(1)
			fmt.Println(file, ":", line, "[ERROR]", err)
		}
	}
	return result
}
