package domain

import (
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/utils"
)

const (
	MessageMediaPushNotif = "push_notif"
	MessageMediaWhatsapp  = "whatsapp"
	MessageMediaEmail     = "email"
)

type ScheduleMessage struct {
	ID            int         `json:"id,omitempty"`
	Title         string      `json:"title,omitempty"`
	Message       string      `json:"message,omitempty"`
	TimeDeliver   int64       `json:"time_deliver,omitempty"`
	TimeSent      int         `json:"time_sent,omitempty"`
	DataCreated   int64       `json:"data_created,omitempty"`
	Media         string      `json:"media,omitempty"`
	Receiver      string      `json:"receiver,omitempty"`
	Status        string      `json:"status,omitempty"`
	AdminFkid     int         `json:"admin_fkid,omitempty"`
	IdentifierID  interface{} `json:"identifier_id,omitempty"`
	SentVia       string      `json:"sent_via,omitempty"`
	Attachments   string      `json:"attachments,omitempty"`
	MessageDetail string      `json:"message_detail,omitempty"` //in db, would be: notification_detail
}

type NotificationDetail struct {
	NotificationData NotificationData `json:"notification_data,omitempty"`
	NotificationType string           `json:"notification_type,omitempty"`
}

type NotificationData struct {
	ID             int    `json:"id,omitempty"`
	PromotionBuyID int    `json:"promotion_buy_id,omitempty"`
	SalesID        string `json:"sales_id,omitempty"`
}

func (s ScheduleMessage) ToMap() map[string]interface{} {
	result := map[string]interface{}{
		"title":        s.Title,
		"message":      s.Message,
		"time_deliver": s.TimeDeliver,
		"media":        s.Media,
		"receiver":     s.Receiver,
		"data_created": s.DataCreated,
	}

	if s.MessageDetail != "" {
		result["notification_detail"] = s.MessageDetail
	}

	if s.IdentifierID != nil && s.IdentifierID != "" {
		result["identifier_id"] = s.IdentifierID
	}
	if s.SentVia != "" {
		result["sent_via"] = s.SentVia
	}
	if s.AdminFkid > 0 {
		result["admin_fkid"] = s.AdminFkid
	}
	if s.DataCreated == 0 {
		result["data_created"] = time.Now().Unix() * 1000
	}
	if s.Attachments != "" && s.Attachments != "[]" {
		result["attachments"] = s.Attachments
	}
	if s.TimeDeliver == 0 {
		result["time_deliver"] = time.Now().Unix() * 1000
	}
	if s.Media == "" {
		if utils.IsNumber(s.Receiver) {
			result["media"] = "whatsapp"
		} else if utils.IsValidEmailAddress(s.Receiver) {
			result["media"] = "email"
		} else {
			result["media"] = "push_notif"
		}
	}
	return result
}

type ScheduleMessageFilter struct {
	Receiver                string `json:"receiver,omitempty"`
	Title                   string
	TimeDeliver             int64
	TimeDeliverOffsetMinute int64
}
