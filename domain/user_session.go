package domain

import (
	"github.com/valyala/fasthttp"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
)

type UserSession struct {
	AdminId      int64  `json:"admin_id,omitempty"`
	MemberId     int64  `json:"member_id,omitempty"`
	MemberAccess string `json:"member_access,omitempty"`
}

const (
	AccessAdmin  = "admin"
	AccessMember = "member"
)

func UserSessionFastHttp(ctx *fasthttp.RequestCtx) UserSession {
	adminId := ctx.Request.Header.Peek("uid")
	memberId := ctx.Request.Header.Peek("mid")
	access := ctx.Request.Header.Peek("access")

	return UserSession{
		AdminId:      utils.ToInt64(adminId),
		MemberId:     utils.ToInt64(memberId),
		MemberAccess: utils.ToString(access),
	}
}
