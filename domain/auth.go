package domain

import "gitlab.com/uniqdev/backend/api-membership/models"

type Auth struct{}

type AuthUseCase interface {
	LoginWithSSO(authToken, idToken string, adminId int) (map[string]interface{}, error)
	SendAuthLink(contact, media string, user UserSession) (map[string]interface{}, error)
	Logout(user UserSession) error
	SendOtp(otp *models.OtpRequestInput, user UserSession) (*models.OtpRequestResponse, error)
	ValidateOtp(token, code string, user UserSession) (*models.AuthToken, error)
}

type AuthRepository interface {
	FetchMemberEmailByPhone(phone string, user UserSession) (string, error)
	FetchMemberByContact(contact, contactType string) (*models.MemberEntity, error)
	FetchMemberByAdmin(memberId, adminId int64) (*models.MemberWithDetail, error)
	AddScheduleMessage(data ...ScheduleMessage) error
	FetchAdmin(adminId int64) (map[string]interface{}, error)
	RemoveFirebaseToken(user UserSession) error
	SetPhoneVerified(memberID int) error

	AddOtp(otp *models.OtpRequestInput, token string, expiredAt int64) (int64, error)
	FetchOtp(token string) (*models.AuthOTPEntity, error)
	RemoveAuthOtp(id int64) error
}

type AuthFirebaseRepository interface {
}
