package usecase

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"os"
	"sort"
	"strings"
	"sync"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/array"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

var muRunPromotion sync.Mutex
var muPaymentUpdate sync.Mutex

func (p *promotionUseCase) RunPromotionRole(param ...domain.PromotionRoleParam) {
	log.Info("RunPromotionRole called...")
	muRunPromotion.Lock()
	defer muRunPromotion.Unlock()

	log.Info("running promotion role... with param given: %v", param)

	promotionRoles, err := p.repo.FetchPromotionRole(param...)
	log.IfError(err)

	log.Info("%d promotions role", len(promotionRoles))
	for _, role := range promotionRoles {
		sql := `
		SELECT mb.member_id, md.firebase_token from members mb 
join members_detail md on md.member_fkid=mb.member_id
where md.admin_fkid=?
and [role]
and mb.member_id not in (select member_fkid from promotion_buy where promotion_fkid=?)
		`
		fmt.Printf("check role: %s, adminId: %v, promoId: %v\n", role.Role, role.AdminFkid, role.PromotionFkid)
		if role.Role == "" || strings.TrimSpace(role.Role) == "" {
			log.Info("role is empty: '%v', set role to inactive %v", role.Role, role.ID)
			_, err = db.Update("crm_promotion_role", map[string]interface{}{
				"status": "inactive",
			}, "id = ?", role.ID)
			log.IfError(err)
			continue
		}

		//to handle if date_of_birth is 0, as this will always result in january
		if strings.Contains(role.Role, "mb.date_of_birth") {
			role.Role = fmt.Sprintf(" %v AND mb.date_of_birth != 0 ", role.Role)
			log.Info("role updated: %v", role.Role)
		}
		sql = strings.Replace(sql, "[role]", role.Role, -1)
		members, err := db.QueryArray(sql, role.AdminFkid, role.PromotionFkid)
		if log.IfError(err) {
			continue
		}

		fmt.Println("total eligible: ", len(members))
		//get the promotion detail
		promo, err := db.Query("SELECT name, promo_nominal, photo, term from promotions where promotion_id=? and admin_fkid=?", role.PromotionFkid, role.AdminFkid)
		log.IfError(err)

		app, err := p.repoApp.FetchAppInfoAndConfig(domain.UserSession{AdminId: cast.ToInt64(role.AdminFkid)})
		log.IfError(err)

		memberIds := make([]string, len(members))
		scheduleMsgs := make([]domain.ScheduleMessage, 0)
		for _, member := range members {
			fmt.Println("inserting...", member["member_id"])
			memberIds = append(memberIds, cast.ToString(member["member_id"]))
			resp, err := db.Insert("promotion_buy", map[string]interface{}{
				"member_fkid":         member["member_id"],
				"promotion_fkid":      role.PromotionFkid,
				"promotion_type_fkid": 15,
				"promo_nominal":       promo["promo_nominal"],
				"price":               0,
				"price_type":          "free",
				"status":              "available",
				"source":              "broadcast",
				"time_created":        time.Now().Unix() * 1000,
				"time_modified":       time.Now().Unix() * 1000,
			})
			if log.IfError(err) {
				continue
			}

			promotionBuyId, err := resp.LastInsertId()
			log.IfError(err)
			log.Info("promotionBuyId: %v", promotionBuyId)

			dynamicLink, err := p.generateDynamicLink(app, "voucher", promotionBuyId)
			log.IfError(err)

			//adding push notif message
			title := GetRandomGiftPromoTitle()
			message := fmt.Sprintf("'%s' \n\n%s \n\nGUNAKAN SEKARANG:\n %s", strings.ToUpper(cast.ToString(promo["name"])), cast.ToString(promo["term"]), dynamicLink)

			notifData := map[string]interface{}{
				"notification_type": "promotion",
				"notification_data": map[string]interface{}{
					"id":               role.PromotionFkid,
					"promotion_buy_id": role.PromotionFkid,
				},
			}

			if member["firebase_token"] == nil {
				log.Info("member '%v' has no firebase token", member["member_id"])

				//add to inbox table directly
				_, err = db.Insert("system_notification", map[string]interface{}{
					"title":             title,
					"message":           message,
					"data_created":      time.Now().Unix() * 1000,
					"type":              "member",
					"receiver_id":       member["member_id"],
					"admin_fkid":        role.AdminFkid,
					"notification_type": "promotion",
					"notification_data": utils.SimplyToJson(notifData["notification_data"]),
				})
				log.IfError(err)

				continue
			}

			scheduleMsgs = append(scheduleMsgs, domain.ScheduleMessage{
				Title:         title,
				Message:       message,
				TimeDeliver:   time.Now().Unix() * 1000,
				Media:         domain.MessageMediaPushNotif,
				Receiver:      cast.ToString(member["firebase_token"]),
				MessageDetail: utils.SimplyToJson(notifData),
				Attachments:   utils.SimplyToJson([]interface{}{promo["photo"]}),
			})
		}

		err = p.repoCampaign.AddScheduleMessage(scheduleMsgs...)
		log.IfError(err)

		log.Info("%d members will gain promotion: %v (%s) | with role: %v | memberIds: %v", len(members), role.PromotionFkid, promo["name"], role.Role, strings.Join(memberIds, ","))
	}
	log.Info("finish running promotion role!")
}

func (p *promotionUseCase) SendNotifyPromotion() {
	data, err := p.repo.FetchNotifyPromotion()
	if log.IfError(err) {
		return
	}

	log.Info("total user to be notified: --------------- %d", len(data))

	//TODO: validate if deals kuota still available

	notifyIds := make([]interface{}, 0)
	members := make([]models.MemberDetail, 0)
	for _, row := range data {
		members = append(members, models.MemberDetail{
			AdminId:  cast.ToInt(row["admin_fkid"]),
			MemberId: cast.ToInt(row["member_fkid"]),
		})
		notifyIds = append(notifyIds, row["crm_notify_product_id"])
	}

	notifTokens, err := p.repo.FetchNotificationToken(members...)
	if log.IfError(err) {
		return
	}

	memberTokenMap := make(map[string][]string)
	for _, token := range notifTokens {
		key := fmt.Sprintf("%v_%v", token["member_fkid"], token["admin_fkid"])
		if _, ok := memberTokenMap[key]; !ok {
			memberTokenMap[key] = make([]string, 0)
		}

		memberTokenMap[key] = append(memberTokenMap[key], cast.ToString(token["token"]))
	}

	//generating schedule message
	// msg := domain.ScheduleMessage{
	// 	Title:       "Deals Alert!",
	// 	Message:     "Promo yang kamu tunggu-tunggu sudah bisa kamu klaim sekarang, jangan sampai kelewatan!",
	// 	TimeDeliver: time.Now().Unix() * 1000,
	// 	DataCreated: time.Now().Unix() * 1000,
	// 	Media:       "push_notif",
	// }

	scheduledMessage := make([]domain.ScheduleMessage, 0)
	for _, notify := range data {
		key := fmt.Sprintf("%v_%v", notify["member_fkid"], notify["admin_fkid"])
		for _, token := range memberTokenMap[key] {
			attachment := make([]string, 0)
			if photo := strings.TrimSpace(cast.ToString(notify["photo"])); photo != "" {
				attachment = append(attachment, photo)
			}
			scheduledMessage = append(scheduledMessage, domain.ScheduleMessage{
				Title:        "Deals Alert!",
				Message:      fmt.Sprintf("Promo %s sudah bisa kamu klaim sekarang, jangan sampai kelewatan!", notify["name"]),
				TimeDeliver:  cast.ToInt64(notify["publish_date"]),
				DataCreated:  time.Now().Unix() * 1000,
				Media:        "push_notif",
				Receiver:     token,
				Attachments:  utils.SimplyToJson(attachment),
				IdentifierID: notify["crm_notify_product_id"],
				MessageDetail: utils.SimplyToJson(map[string]interface{}{
					"notification_type": "promotion",
					"notification_data": map[string]interface{}{
						"id": notify["promotion_fkid"],
					},
				}),
			})
		}
	}

	log.Info("total message: %v", len(scheduledMessage))
	p.repoCampaign.AddScheduleMessage(scheduledMessage...)

	//remove from database
	p.repo.RemoveNotifyPromotion(notifyIds...)
}

func (p *promotionUseCase) ReceivePaymentUpdate(promotionBuyId int, transactionId, status string) error {
	muPaymentUpdate.Lock()
	defer muPaymentUpdate.Unlock()

	promotionBuys, err := p.repo.FetchPromotionBuyPayment(models.PromotionBuyPayment{
		PromotionBuyId: int64(promotionBuyId),
		TransactionId:  transactionId,
	})
	if log.IfError(err) {
		return err
	}

	log.Info("promotionBuy %v ", promotionBuys)
	if len(promotionBuys) == 0 {
		log.IfError(fmt.Errorf("receive  promotion buy payment but can not find the data, promoBuyId: %v | transId: %v", promotionBuyId, transactionId))
		return fmt.Errorf("not found")
	}

	promo := promotionBuys[0]
	user := domain.UserSession{AdminId: cast.ToInt64(promo["admin_fkid"]), MemberId: cast.ToInt64(promo["member_fkid"])}

	//if promo already paid/used, skipp....
	if cast.ToString(promo["promotion_buy_status"]) != models.PromotionBuyStatusPending {
		return nil
	}

	paymentStatus := "expire"
	if array.Contain([]string{"settlement", "success", "succeeded"}, strings.ToLower(status)) {
		paymentStatus = "paid"
	}

	err = p.repo.UpdatePromotionBuyPaymentStatus(cast.ToInt64(promotionBuys[0]["promotion_buy_payment_id"]), paymentStatus)
	log.IfError(err)

	//if expired, remove the promotion buy
	//but, makes sure no pending payment, and current status is pending
	if status == "expire" && cast.ToString(promo["promotion_buy_status"]) == "pending" {
		pendings, err := p.repo.FetchPendingPayment(cast.ToInt(promo["promotion_id"]), user)
		if err == nil && len(pendings) == 0 {
			p.repo.RemovePromotionBuy(promotionBuyId)
		}
	}

	//if not paid, return ...
	if paymentStatus != "paid" {
		return nil
	}

	//update promotion buy status
	err = p.repo.UpdatePromotionBuyStatus(promotionBuyId, "available")
	if log.IfError(err) {
		return err
	}

	//notify user
	members, err := p.repoCampaign.FetchMember(int(user.AdminId), int(user.MemberId))
	if log.IfError(err) {
		return err
	}

	if len(members) == 0 {
		log.Info("member not found... adminId: %v | memberId: %v", promo["admin_fkid"], promo["member_fkid"])
		return fmt.Errorf("member data not found")
	}

	memberDetailId := cast.ToInt64(members[0]["members_detail_id"])
	tokens, err := p.repoCampaign.FetchMemberNotifToken(memberDetailId)
	if log.IfError(err) {
		return err
	}
	log.Info("total member (%v) token: %v", members[0]["members_detail_id"], len(tokens))

	message := fmt.Sprintf("Pembelian Deals <b>%s</b> kamu sudah berhasil dikonfirmasi", promo["name"])
	promoLink, err := p.helperUseCase.FetchShareLink("voucher", cast.ToString(promotionBuyId), domain.UserSession{AdminId: cast.ToInt64(promo["admin_fkid"])})
	if !log.IfError(err) {
		message += fmt.Sprintf("<br/><br/><b><u><a href='%s'>GUNAKAN SEKARANG</a></b></u>", promoLink["shortLink"])
	}

	scheduledMessages := make([]domain.ScheduleMessage, 0)
	for _, token := range tokens {
		scheduledMessages = append(scheduledMessages, domain.ScheduleMessage{
			Title:        "Pembelian Deals Berhasil",
			Message:      message,
			TimeDeliver:  time.Now().Unix() * 1000,
			DataCreated:  time.Now().Unix() * 1000,
			Media:        "push_notif",
			Receiver:     cast.ToString(token["token"]),
			IdentifierID: promotionBuyId,
			MessageDetail: utils.SimplyToJson(map[string]interface{}{
				"notification_type": "voucher",
				"notification_data": map[string]interface{}{
					"id": promotionBuyId,
				},
			}),
		})
	}

	p.repoCampaign.AddScheduleMessage(scheduledMessages...)

	return nil
}

var muRunPromotionReminder sync.Mutex

func (p *promotionUseCase) RunPromotionReminder(dayLeft int) {
	muRunPromotionReminder.Lock()
	defer muRunPromotionReminder.Unlock()

	// Example: Fetch promotions expiring in exactly 3 days
	filter := models.PromotionBuyFilter{
		ExactExpireDay: dayLeft,
		Status:         models.PromotionBuyStatusAvailable,
	}

	isReminderByPushNotif := dayLeft >= 0 // day before 5, send push notif, then 3 days before, send by whatsapp

	promoBuys, err := p.repo.FetchPromotionBuy(filter)
	log.Info("promo will expire in %v days: %v", filter.ExactExpireDay, len(promoBuys))
	if log.IfError(err) || len(promoBuys) == 0 {
		return
	}

	//sort promotBuys by id (desc)
	sort.Slice(promoBuys, func(i, j int) bool {
		return promoBuys[i].PromotionBuyID > promoBuys[j].PromotionBuyID
	})

	// Group promotions by member ID
	promoBuyByMember := make(map[int][]models.PromotionBuyEntity)
	memberIdsMap := make(map[int]bool)
	promotionIdsMap := make(map[int]bool)

	for _, promoBuy := range promoBuys {
		memberIdsMap[(promoBuy.MemberFkid)] = true
		promotionIdsMap[(promoBuy.PromotionFkid)] = true
		promoBuyByMember[promoBuy.MemberFkid] = append(promoBuyByMember[promoBuy.MemberFkid], promoBuy)
	}

	log.Info("Found %d unique members with expiring promotions", len(memberIdsMap))
	log.Info("Found %d unique promotions expiring", len(promotionIdsMap))

	memberIds := array.GetKeysOfMap(memberIdsMap)
	promotionIds := array.GetKeysOfMap(promotionIdsMap)

	log.Info("memberIds: %v", memberIds)
	log.Info("promotionIds: %v", promotionIds)

	var members []map[string]interface{}
	var promotions []map[string]interface{}
	var memberErr, promoErr error

	var wg sync.WaitGroup
	wg.Add(2)

	// Fetch members in goroutine
	go func() {
		defer wg.Done()
		members, memberErr = p.repoUser.FetchMemberByIds(memberIds...)
	}()

	// Fetch promotions in goroutine
	go func() {
		defer wg.Done()
		promotions, promoErr = p.repo.FetchPromotion(models.PromotionDetailFilter{
			PromotionIds: promotionIds,
		})
	}()

	var memberTokens []models.NotificationToken
	var tokenErr error
	if isReminderByPushNotif {
		wg.Add(1)
		go func() {
			defer wg.Done()
			memberTokens, tokenErr = p.repoUser.FetchMemberNotificationToken(memberIds...)
			log.Info("total member tokens: %v, size memberIds: %v", len(memberTokens), len(memberIds))
			log.IfError(tokenErr)
		}()
	}

	wg.Wait()

	if log.IfError(memberErr) || log.IfError(promoErr) {
		return
	}

	crmAppMap := make(map[int]domain.CrmApp)
	adminIdMap := make(map[int]int)
	for _, promo := range promotions {
		adminIdMap[cast.ToInt(promo["admin_fkid"])] = cast.ToInt(promo["admin_fkid"])
	}

	log.Info("adminIdMap: %v", adminIdMap)
	adminMap := make(map[int]map[string]interface{})
	for id := range adminIdMap {
		crmApp, err := p.repoApp.FetchAppInfoAndConfig(domain.UserSession{AdminId: cast.ToInt64(id)})
		log.IfError(err)
		crmAppMap[id] = crmApp

		admin, err := p.repoUser.FetchAdmin(cast.ToInt64(id))
		log.IfError(err)
		adminMap[id] = admin
	}

	memberMap := array.FlatMapArray(members, "member_id")
	promotionMap := array.FlatMapArray(promotions, "promotion_id")

	memberTokenMap := make(map[int][]string)
	for _, token := range memberTokens {
		memberTokenMap[cast.ToInt(token.MemberId)] = append(memberTokenMap[cast.ToInt(token.MemberId)], cast.ToString(token.Token))
	}

	scheduledMessages := p.setReminderMessage(promoBuyByMember, memberMap, memberTokenMap, promotionMap, crmAppMap, adminMap, filter)
	log.Info("total scheduled message: %v", len(scheduledMessages))
	err = p.repoCampaign.AddScheduleMessage(scheduledMessages...)
	log.IfError(err)
}

func (p *promotionUseCase) setReminderMessage(promoBuyByMember map[int][]models.PromotionBuyEntity, memberMap map[string]map[string]interface{}, memberTokenMap map[int][]string, promotionMap map[string]map[string]interface{}, crmAppMap map[int]domain.CrmApp, adminMap map[int]map[string]interface{}, filter models.PromotionBuyFilter) []domain.ScheduleMessage {
	var scheduledMessages []domain.ScheduleMessage
	mediaWaCounter := 0
	mediaEmailCounter := 0

	//create notification message
	for memberId, promoBuys := range promoBuyByMember {
		member := memberMap[cast.ToString(memberId)]
		tokens := memberTokenMap[memberId]

		// if isReminderByPushNotif && len(tokens) == 0 {
		// 	log.Info("member has no token: %v | %v", member["name"], member["email"])
		// 	continue
		// }

		log.Info("------> member: %v | %v", member["name"], member["email"])
		promoList := make([]string, 0)
		var adminId int
		for _, promoBuy := range promoBuys {
			promotion := promotionMap[cast.ToString(promoBuy.PromotionFkid)]
			adminId = cast.ToInt(promotion["admin_fkid"])
			crmApp := crmAppMap[adminId]

			log.Info("   promotion: %v (adminId: %v)", promotion["name"], adminId)
			link, err := p.generateDynamicLink(crmApp, "voucher", cast.ToString(promoBuy.PromotionBuyID))
			log.IfError(err)
			promoList = append(promoList, fmt.Sprintf(" - %s %s", promotion["name"], link))
		}

		admin := adminMap[adminId]
		message := GetRandomPromoReminder(filter.ExactExpireDay, strings.Join(promoList, "\n"), cast.ToString(admin["business_name"]))
		log.Info("\n\n\nmessage: %v", message)

		log.Info("member has %d tokens: %v | %v", len(tokens), member["name"], member["email"])
		for _, token := range tokens {
			scheduledMessages = append(scheduledMessages, domain.ScheduleMessage{
				Title:       GetRandomPromoReminderTitle(),
				Message:     message,
				TimeDeliver: time.Now().UnixMilli() + rand.Int63n(18000000), //add random number
				DataCreated: time.Now().UnixMilli(),
				Media:       domain.MessageMediaPushNotif,
				Receiver:    token,
			})
		}

		//if user has no token, send thorugh email/wa
		if len(tokens) == 0 && mediaEmailCounter >= 100 {
			media := domain.MessageMediaPushNotif
			receiver := cast.ToString(member["email"])
			if mediaWaCounter < 100 && filter.ExactExpireDay < 5 {
				media = domain.MessageMediaWhatsapp
				receiver = cast.ToString(member["phone"])
				message = fmt.Sprintf("*%s* \n\n%s", GetRandomPromoReminderTitle(), message)
				mediaWaCounter++
			} else {
				message = strings.ReplaceAll(message, "\n", " <br/> ")
				mediaEmailCounter++
			}

			scheduledMessages = append(scheduledMessages, domain.ScheduleMessage{
				Title:       GetRandomPromoReminderTitle(),
				Message:     message,
				TimeDeliver: time.Now().UnixMilli() + rand.Int63n(18000000), //add random number
				DataCreated: time.Now().UnixMilli(),
				Media:       media,
				Receiver:    receiver,
			})
		}

		// if mediaEmailCounter >= 100 {
		// 	log.Info("total scheduled message: %v --- skip for now", len(scheduledMessages))
		// 	break
		// }
	}
	return scheduledMessages
}

func (p *promotionUseCase) generateDynamicLink(crmApp domain.CrmApp, linkType string, id interface{}) (string, error) {
	// Try to get from cache first
	cachedLink, err := p.repo.GetDynamicLinkCache(cast.ToString(id))
	if err == nil && cachedLink != "" {
		log.Info("using cached dynamic link for %v: %v", id, cachedLink)
		return cachedLink, nil
	}

	crmAppInfo := crmApp.ToAppInfo()
	link := fmt.Sprintf("%s/%s/%s", crmAppInfo.Web.Url, linkType, id)

	//create dynamic link
	req := utils.HttpRequest{
		Method: "POST",
		Url:    fmt.Sprintf("https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key=%s", os.Getenv("FIREBASE_CRM_KEY")),
		PostRequest: utils.PostRequest{Body: map[string]interface{}{
			"dynamicLinkInfo": map[string]interface{}{
				"domainUriPrefix": FirebaseDynamicLink(),
				"link":            link,
				"androidInfo": map[string]interface{}{
					"androidPackageName": crmAppInfo.Android.PackageName,
				},
				"iosInfo": map[string]interface{}{
					"iosBundleId": crmAppInfo.Ios.BundleID,
				},
			},
			"suffix": map[string]interface{}{
				"option": "SHORT",
			},
		}},
	}
	res, err := req.ExecuteRequest()
	log.IfError(err)

	var respMap map[string]interface{}
	if !log.IfError(json.Unmarshal(res, &respMap)) {
		if dynamicLink := cast.ToString(respMap["shortLink"]); dynamicLink != "" {
			log.Info("dynamic link created: %v", dynamicLink)

			// Store in cache
			err = p.repo.SetDynamicLinkCache(cast.ToString(id), dynamicLink)
			if err != nil {
				log.Error("failed to cache dynamic link: %v", err)
			}

			return dynamicLink, nil
		} else {
			log.Error("firebase dynamic link doesn't return shortLink. %v", string(res))
			return "", fmt.Errorf("firebase dynamic link doesn't return shortLink. %v", string(res))
		}
	} else {
		fmt.Println("err json from firebase: " + string(res))
	}

	return "", fmt.Errorf("firebase dynamic link doesn't return shortLink. %v", string(res))
}

func FirebaseDynamicLink() string {
	if link := os.Getenv("FIREBASE_DYNAMIC_LINK"); link != "" {
		return link
	}

	env := os.Getenv("server")
	if env == "demo" || env == "staging" || env == "production" {
		return "https://uniqapps.page.link"
	}
	return "https://uniqcrm.page.link"
}
