package http

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"reflect"
	"strings"

	fasthttprouter "github.com/buaazp/fasthttprouter"
	fasthttp "github.com/valyala/fasthttp"
	v1 "gitlab.com/uniqdev/backend/api-membership/controller/v1"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
	"gitlab.com/uniqdev/backend/api-membership/module/promotion"
)

type paymentHandler struct {
	usecase          domain.PaymentUseCase
	usecasePromotion promotion.UseCase
}

func NewHttpPaymentHandler(app *fasthttprouter.Router,
	useCase domain.PaymentUseCase, useCasePromo promotion.UseCase) {
	handler := &paymentHandler{useCase, useCasePromo}

	//webhook
	app.POST("/payment/notification", handler.WebhookPayment)
}

func (h paymentHandler) WebhookPayment(ctx *fasthttp.RequestCtx) {
	log.Info("receive payment pubsub: %s", string(ctx.PostBody()))

	//handle from pubsub
	var pubsub models.PubSub
	err := json.NewDecoder(bytes.NewReader(ctx.PostBody())).Decode(&pubsub)
	if log.IfError(err) {
		return
	}

	data, err := base64.StdEncoding.DecodeString(pubsub.Message.Data)
	if log.IfError(err) {
		return
	}

	log.Info("pubsub receive data: %s", string(data))
	var payload map[string]interface{}
	if log.IfError(json.Unmarshal(data, &payload)) {
		return
	}

	log.Info("receive from pubsub, payload: %v", utils.SimplyToJson(payload))
	fmt.Println("payload data type: ", reflect.TypeOf(payload["payload"]))

	paymentData := getTransactionDetail(payload)
	log.Info("order id: '%s' | transId: %v | status: %v", paymentData.OrderId, paymentData.TransactionId, paymentData.Status)

	if paymentData.OrderId != "" {
		go v1.HandlePaymentChange(paymentData.OrderId)
	}

	if utils.IsNumber(paymentData.OrderId) {
		go h.usecasePromotion.ReceivePaymentUpdate(cast.ToInt(paymentData.OrderId), paymentData.TransactionId, paymentData.Status)
	}

	ctx.SetStatusCode(fasthttp.StatusOK)
	// _ = json.NewEncoder(ctx).Encode(map[string]interface{}{"message": "this is sample of payment feature"})
}

func getTransactionDetail(payload map[string]interface{}) models.PaymentWebhookPayload {
	if cast.ToString(payload["payment_gateway"]) == "xendit" {
		var paymentXendit models.PaymentWebhookXendit
		payloadData, err := json.Marshal(payload["payload"])
		log.IfError(err)
		fmt.Println("payload data --> ", string(payloadData))
		log.IfError(json.Unmarshal([]byte(payloadData), &paymentXendit))
		return paymentXendit.ToPayload()
	}

	if payloadData, ok := payload["payload"].(map[string]interface{}); ok {
		return models.PaymentWebhookPayload{
			TransactionId: strings.TrimSpace(cast.ToString(payloadData["transaction_id"])),
			OrderId:       strings.TrimSpace(cast.ToString(payloadData["order_id"])),
			Status:        cast.ToString(payloadData["transaction_status"]),
		}
	}
	return models.PaymentWebhookPayload{}
}
