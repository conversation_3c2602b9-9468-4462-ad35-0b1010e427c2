package campaign

import "gitlab.com/uniqdev/backend/api-membership/domain"

type CampaignRepository interface {
	AddCampaign(campaign domain.Campaign, user domain.UserSession) (int64, error)
	FetchMember(adminId int, memberId ...int) ([]map[string]interface{}, error)
	FetchMemberNotifToken(memberDetailId ...int64) ([]map[string]interface{}, error)

	AddScheduleMessage(message ...domain.ScheduleMessage) error
	FetchScheduleMessage(filter domain.ScheduleMessageFilter) ([]domain.ScheduleMessage, error)
	RemoveCampaign(id int, user domain.UserSession) error
	RemoveScheduledMessageByIdentifier(identifierId interface{}) error
}
