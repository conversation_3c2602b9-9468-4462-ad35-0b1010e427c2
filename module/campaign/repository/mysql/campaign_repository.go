package mysql

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	db "gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/module/campaign"
)

type campaignRepository struct {
	db db.Repository
}

func NewMysqlCampaignRepository(conn *sql.DB) campaign.CampaignRepository {
	return &campaignRepository{db.Repository{Conn: conn}}
}

// func (c campaingRepository) AddCampaign(campaign domain.Campaign, user domain.UserSession) (int64, error)
func (c campaignRepository) AddCampaign(campaign domain.Campaign, user domain.UserSession) (int64, error) {
	var campaignId int64
	err := db.WithTransaction(func(t db.Transaction) error {
		res, _ := t.Insert("crm_campaign", map[string]interface{}{
			"name":          campaign.Name,
			"title":         campaign.Title,
			"content":       campaign.Content,
			"admin_fkid":    user.AdminId,
			"total_contact": len(campaign.Member),
			"send_date":     campaign.DateTimeMillis,
			"created":       time.Now().Unix() * 1000,
		})
		campaignId, _ = res.LastInsertId()

		for i, operator := range campaign.Role.Operator {
			res, _ = t.Insert("crm_campaign_role", map[string]interface{}{
				"campaign_fkid": campaignId,
				"role":          strings.ToLower(operator),
			})

			roleId, _ := res.LastInsertId()
			roleDetail := campaign.Role.Detail[i]

			t.Insert("crm_campaign_role_detail", map[string]interface{}{
				"role_fkid":      roleId,
				"role_condition": roleDetail.Condition,
				"role_where":     roleDetail.ConditionValue,
				"role_value":     roleDetail.InputConditionValue,
			})
		}

		campaignMember := make([]map[string]interface{}, 0)
		for _, member := range campaign.Member {
			campaignMember = append(campaignMember, map[string]interface{}{
				"campaign_fkid": campaignId,
				"member_fkid":   member.MemberID,
				"media":         utils.SimplyToJson(member.Media),
			})
			// t.Insert("crm_campaign_member_send", map[string]interface{}{
			// 	"campaign_fkid": campaignId,
			// 	"member_fkid":   member.MemberID,
			// 	"media":         utils.SimplyToJson(member.Media),
			// })
		}

		// Split the campaignMember slice into smaller batches of max 3k data each
		batchSize := 3000
		startTime := time.Now()
		for i := 0; i < len(campaignMember); i += batchSize {
			end := i + batchSize

			// Check we are not exceeding the slice bounds
			if end > len(campaignMember) {
				end = len(campaignMember)
			}

			// Insert the batch
			t.InsertBatch("crm_campaign_member_send", campaignMember[i:end])
		}
		elapsedTime := time.Since(startTime)
		if elapsedTime > 40*time.Second {
			log.IfError(fmt.Errorf("Inserting campaignMember took more than 40 seconds"))
		}

		return nil
	})
	return campaignId, err
}

func (c campaignRepository) FetchMember(adminId int, memberId ...int) ([]map[string]interface{}, error) {
	sql := `SELECT name, email, phone, md.firebase_token, md.members_detail_id, m.member_id,
	total_point, type_fkid, md.status 
	from members m 
	join members_detail md on md.member_fkid=m.member_id
	where md.admin_fkid = @adminId `

	if len(memberId) > 0 {
		sql += " AND m.member_id IN @memberId "
	}
	sql, params := db.MapParam(sql, map[string]interface{}{
		"adminId":  adminId,
		"memberId": memberId,
	})
	return db.QueryArray(sql, params...)
}
func (c campaignRepository) FetchMemberNotifToken(memberDetailId ...int64) ([]map[string]interface{}, error) {
	sql := `SELECT user_id, token from user_notification_token 
	where user_type='member' and app='crm'
	and user_id in @memberDetailId `
	sql, params := db.MapParam(sql, map[string]interface{}{
		"memberDetailId": memberDetailId,
	})
	return db.QueryArray(sql, params...)
}

func (c campaignRepository) AddScheduleMessage(message ...domain.ScheduleMessage) error {
	// err := db.WithTransaction(func(t db.Transaction) error {
	// 	for _, m := range message {
	// 		t.Insert("scheduled_message", m.ToMap())
	// 	}
	// 	return nil
	// })

	if len(message) == 0 {
		fmt.Println("skip... no schedule message passed!")
		return nil
	}

	dataMap := make([]map[string]interface{}, 0)
	for _, m := range message {
		dataMap = append(dataMap, m.ToMap())
	}
	// err := db.BulkInsert("scheduled_message", dataMap)

	// dataChunks := array.ChunkMapArray(dataMap, 5000)

	var err error
	logInserted := 0
	maxData := 2500
	startAt := time.Now()
	//chunk the data
	for i := 0; i <= len(dataMap); i += maxData {
		j := i + maxData
		if j > len(dataMap) {
			j = len(dataMap)
		}
		chunkData := dataMap[i:j]
		logInserted += len(chunkData)
		var resp sql.Result
		resp, err = db.BulkInsert("scheduled_message", chunkData)
		if !log.IfError(err) {
			lastInsertId, _ := resp.LastInsertId()
			numRows, _ := resp.RowsAffected()
			log.Info("last insert id: %v, numRows: %v, chunkData: %v", lastInsertId, numRows, chunkData)
		}
	}

	if logInserted != len(dataMap) {
		log.IfError(fmt.Errorf("total inserted campaign to the same as total data, %v should %v", logInserted, len(dataMap)))
	}
	log.Info("insert %v campaigns took %v", len(dataMap), time.Since(startAt))
	if time.Since(startAt).Seconds() > 40 {
		log.IfError(fmt.Errorf("insert %v campaigns took %v", len(dataMap), time.Since(startAt)))
	}

	return err
}

func (c campaignRepository) RemoveCampaign(id int, user domain.UserSession) error {
	//crm_campaign_role_detail,crm_campaign_role,crm_campaign_member_send,crm_campaign
	log.Info("delete campaign: %v (%v)", id, user.AdminId)
	err := db.WithTransaction(func(t db.Transaction) error {
		campaignId, err := db.Query("select campaign_id from crm_campaign where campaign_id = ? and admin_fkid = ?", id, user.AdminId)
		if err != nil {
			return err
		}

		if len(campaignId) == 0 {
			log.Info("campaign not found! %v", campaignId)
			return fmt.Errorf("campaign not found: %v", id)
		}

		roles, err := db.QueryArray("select id from crm_campaign_role where campaign_fkid  = ?", id)
		if err != nil {
			return err
		}

		roleIds := make([]interface{}, 0)
		for _, raw := range roles {
			roleIds = append(roleIds, raw["id"])
		}

		t.Delete("crm_campaign_message", "campaign_fkid =?", id)
		t.Delete("crm_campaign_role_detail", "role_fkid in "+db.WhereIn(len(roleIds)), roleIds...)
		t.Delete("crm_campaign_role", "campaign_fkid = ?", id)
		t.Delete("crm_campaign_member_send", "campaign_fkid =?", id)
		t.Delete("crm_campaign", "campaign_id =?", id)
		t.Delete("scheduled_message", "identifier_id = ?", id)

		return nil
	})
	return err
}

func (c campaignRepository) RemoveScheduledMessageByIdentifier(identifierId interface{}) error {
	resp, err := db.Delete("scheduled_message", "identifier_id = ?", identifierId)
	numRows, _ := resp.RowsAffected()
	log.Info("rows effected of removing scheduled message: %v | id: %v", numRows, identifierId)
	return err
}

// FetchScheduleMessage implements campaign.CampaignRepository.
func (c *campaignRepository) FetchScheduleMessage(filter domain.ScheduleMessageFilter) ([]domain.ScheduleMessage, error) {
	query := `SELECT * FROM scheduled_message WHERE time_deliver BETWEEN @timeStart AND @timeEnd AND receiver = @receiver `
	startTime := filter.TimeDeliver - (60 * filter.TimeDeliverOffsetMinute * 1000) // Subtract x minutes in milliseconds
	endTime := filter.TimeDeliver + (60 * filter.TimeDeliverOffsetMinute * 1000)   // Add x minutes in milliseconds

	if filter.Title != "" {
		query += " AND title = @title"
	}

	fmt.Printf("FetchScheduleMessage, timeDeliver: %v, so startAt: %v, endAt: %v | offset: %v\n", filter.TimeDeliver, startTime, endTime, filter.TimeDeliverOffsetMinute)
	query, params := db.MapParam(query, map[string]interface{}{
		"timeStart": startTime,
		"timeEnd":   endTime,
		"receiver":  filter.Receiver,
		"title":     filter.Title,
	})

	var result []domain.ScheduleMessage
	err := c.db.Query(query, params...).Model(&result)
	return result, err
}
