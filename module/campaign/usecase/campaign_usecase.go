package usecase

import (
	"fmt"
	"os"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/array"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/module/campaign"
)

type campaignUseCase struct {
	repo campaign.CampaignRepository
}

func NewCampaignUseCase(repository campaign.CampaignRepository) campaign.CampaignUseCase {
	return &campaignUseCase{repository}
}

func (c campaignUseCase) AddCampaign(campaign domain.Campaign, user domain.UserSession) (map[string]interface{}, error) {
	//validation
	if len(campaign.Member) == 0 {
		return nil, fmt.Errorf("can not create campaign with no member")
	}

	if len(campaign.Role.Operator) != len(campaign.Role.Detail) {
		return nil, fmt.Errorf("length size of role operator and role detail does not match")
	}

	// fmt.Println("create campaign: ", utils.SimplyToJson(campaign))
	// log.Info("create %v campaign", len())
	campaignId, err := c.repo.AddCampaign(campaign, user)
	if log.IfError(err) {
		return nil, err
	}

	//create scheduled message
	campaign.CampaignId = int(campaignId)
	go c.GenerateScheduleMesageOfCampaign(campaign, user)
	return map[string]interface{}{
		"campaign_id": campaignId,
	}, nil
}

func (c campaignUseCase) GenerateScheduleMesageOfCampaign(campaign domain.Campaign, user domain.UserSession) {
	//fetch data member
	memberIds := make([]int, 0)
	for _, member := range campaign.Member {
		memberIds = append(memberIds, member.MemberID)
	}

	members, err := c.repo.FetchMember(int(user.AdminId), memberIds...)
	if err != nil {
		return
	}

	if len(members) == 0 {
		log.Info("no member found for campaign: %v - %v", campaign.CampaignId, campaign.Title)
		return
	}

	memberMap := array.FlatMapArray(members, "member_id")

	memberDetailIds := make([]int64, 0)
	memberIdToDetailId := make(map[int64]int64)
	for _, member := range members {
		memberDetailIds = append(memberDetailIds, cast.ToInt64(member["members_detail_id"]))
		memberIdToDetailId[cast.ToInt64(member["member_id"])] = memberDetailIds[len(memberDetailIds)-1]
	}

	memberNotifToken, err := c.repo.FetchMemberNotifToken(memberDetailIds...)
	log.IfError(err)
	log.Info("total token all: %v", len(memberNotifToken))

	//adding firebase_token from members_detail table
	memberTokenMap := array.FlatMapArray(members, "firebase_token")
	log.Info("total memberToken: %v", len(memberTokenMap))
	for _, token := range memberNotifToken {
		delete(memberTokenMap, cast.ToString(token["token"]))
	}
	log.Info("(after) total memberToken: %v", len(memberTokenMap))

	//merge
	for token, data := range memberTokenMap {
		memberNotifToken = append(memberNotifToken, map[string]interface{}{
			"token":   token,
			"user_id": data["members_detail_id"],
		})
	}

	// if len(memberTokenMap) > 0 {
	// 	fmt.Println("----- finish ----")
	// 	return
	// }

	fmt.Println("total token: ", len(memberNotifToken))
	log.Info("tokens: %v", memberNotifToken)
	memberToken := array.GroupBy(memberNotifToken, "user_id")

	//generate message
	messages := make([]domain.ScheduleMessage, 0)
	for _, member := range campaign.Member {
		memberData := memberMap[cast.ToString(member.MemberID)]
		if len(memberData) == 0 {
			log.Info("no member for id: %v", member.MemberID)
			continue
		}

		title := campaignReplacer(campaign.Title, memberData)
		message := campaignReplacer(campaign.Content, memberData)

		for _, media := range member.Media {
			if media == "wa" {
				media = domain.CampaignMediaWa
			}

			raw := domain.ScheduleMessage{
				Title:       title,
				Message:     message,
				Media:       media,
				TimeDeliver: campaign.DateTimeMillis,
				DataCreated: time.Now().Unix() * 1000,
			}
			if media == domain.CampaignMediaEmail {
				raw.Receiver = cast.ToString(memberData["email"])
				messages = append(messages, raw)
			} else if media == domain.CampaignMediaWa {
				raw.Receiver = cast.ToString(memberData["phone"])
				raw.SentVia = "WA_USER"
				raw.AdminFkid = int(user.AdminId)
				raw.Message = utils.ExtractHtml(raw.Message)
				messages = append(messages, raw)
			} else if media == domain.CampaignMediaPushNotif {
				memberDetailId := memberIdToDetailId[cast.ToInt64(member.MemberID)]
				tokens := memberToken[cast.ToString(memberDetailId)]
				// fmt.Println("tototal token: ", len(tokens))
				for _, token := range tokens {
					raw.IdentifierID = campaign.CampaignId
					raw.Receiver = cast.ToString(token["token"])
					raw.Media = "push_notif"
					messages = append(messages, raw)
				}
			}
		}
	}
	log.Info("total messages: %v", len(messages))
	if len(messages) == 0 {
		return
	}

	//insert to db (schedule message)
	err = c.repo.AddScheduleMessage(messages...)
	log.IfError(err)
}

func campaignReplacer(text string, member map[string]interface{}) string {
	replacer := strings.NewReplacer("#name", cast.ToString(member["name"]),
		"#nama", cast.ToString(member["name"]),
		"#email", cast.ToString(member["email"]))
	return replacer.Replace(text)
}

func (c campaignUseCase) RemoveCampaign(id int, user domain.UserSession) error {
	err := c.repo.RemoveCampaign(id, user)
	if err != nil {
		return err
	}
	go c.repo.RemoveScheduledMessageByIdentifier(id)
	return nil
}

func (c campaignUseCase) Test() {
	// c.sendUnsentCampaing()
	// sql := `SELECT md.member_fkid, md.members_detail_id, md.firebase_token from members_detail md
	// join crm_campaign_member_send ms on ms.member_fkid=md.member_fkid
	// where md.member_fkid not in (select user_id from user_notification_token where app='crm')
	// and md.firebase_token is not null`
	// memberToken, err := db.QueryArray(sql)
	// if log.IfError(err) {
	// 	return
	// }

	// log.Info("total data: %v", len(memberToken))
	c.sendUnsentCampaing()
}

func (c campaignUseCase) sendUnsentCampaing() {
	if env := os.Getenv("ENV"); env != "localhost" {
		log.Info("can not run testing on env: %v", env)
		return
	}

	log.Info("----------- TESTING CAMPAIGN ---------")
	// sql := `SELECT member_fkid, media from crm_campaign_member_send where campaign_fkid=184`
	sql := `SELECT ms.member_fkid, ms.media from members_detail md 
	join crm_campaign_member_send ms on ms.member_fkid=md.member_fkid
	where md.member_fkid not in (select user_id from user_notification_token where app='crm')
	and md.firebase_token is not null
	and md.admin_fkid=10
	and ms.campaign_fkid=184`
	memberSend, err := db.QueryArray(sql)
	if log.IfError(err) {
		return
	}

	log.Info("total member send: %v", len(memberSend))
	campaignMember := make([]domain.Member, 0)
	for _, row := range memberSend {
		// var media []string
		// mediaByte, err := json.Marshal(row["media"])
		// log.IfError(err)

		// err = json.Unmarshal(mediaByte, &media)
		// log.IfError(err)

		// mediaStr := cast.ToString(row["media"])
		// mediaStr = strings.ReplaceAll(mediaStr, "")

		campaignMember = append(campaignMember, domain.Member{
			MemberID: cast.ToInt(row["member_fkid"]),
			Media:    []string{"push_notification"},
		})
	}

	campaignMap, err := db.Query("SELECT * from crm_campaign where campaign_id=184")
	if log.IfError(err) {
		return
	}

	log.Info("total member: %v", len(memberSend))
	campaign := domain.Campaign{
		CampaignId:     184,
		Name:           cast.ToString(campaignMap["name"]),
		Title:          cast.ToString(campaignMap["title"]),
		Content:        cast.ToString(campaignMap["content"]),
		Member:         campaignMember,
		DateTimeMillis: 1674793028792,
	}

	c.GenerateScheduleMesageOfCampaign(campaign, domain.UserSession{AdminId: 10})
	panic("finish test>>>")
}
