package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"firebase.google.com/go/auth"
	authJwt "gitlab.com/uniqdev/backend/api-membership/core/auth"
	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	"gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/exception"
	"gitlab.com/uniqdev/backend/api-membership/core/firebase"
	"gitlab.com/uniqdev/backend/api-membership/core/google"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	"gitlab.com/uniqdev/backend/api-membership/core/utils"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/generate"
	"gitlab.com/uniqdev/backend/api-membership/core/utils/limiter"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

const (
	verifyCustomTokenURL   = "https://www.googleapis.com/identitytoolkit/v3/relyingparty/verifyCustomToken?key=%s"
	verifyCustomTokenURLV1 = "https://identitytoolkit.googleapis.com/v1/accounts:signInWithCustomToken?key="
)

type authUseCase struct {
	repo        domain.AuthRepository
	repoApp     domain.AppRepository
	rateLimiter limiter.RateLimiter
}

func NewAuthUseCase(repository domain.AuthRepository, appRepo domain.AppRepository) domain.AuthUseCase {
	return &authUseCase{repository, appRepo, *limiter.NewRateLimiter(60*15, 5)}
}

func (a *authUseCase) LoginWithSSO(authToken string, idToken string, adminId int) (map[string]interface{}, error) {
	authSso := AuthWithApple{}
	authResult, err := authSso.ValidateToken(idToken)
	if err != nil {

	}
	fmt.Println("email: ", authResult.Email)
	return map[string]interface{}{}, err
}

func (a *authUseCase) SendAuthLink(contact, media string, user domain.UserSession) (map[string]interface{}, error) {
	contact = strings.TrimSpace(contact)
	//handle number like: 0851-422-5171
	if !strings.Contains(contact, "@") {
		contact = strings.ReplaceAll(contact, "-", "")
	}
	email := contact
	if utils.IsNumber(contact) {
		contact = utils.FormatPhoneNumber(contact)

		var err error
		email, err = a.repo.FetchMemberEmailByPhone(contact, user)
		if log.IfError(err) || strings.TrimSpace(email) == "" {
			return nil, fmt.Errorf("account not registered: %s", contact)
		}
	}

	if strings.TrimSpace(email) == "" || utils.IsNumber(email) {
		log.IfError(fmt.Errorf("can not get email from: '%v' | '%v'", contact, email))
		return nil, fmt.Errorf("invalid contact %s", contact)
	}

	crmApp, err := a.repoApp.FetchAppInfoAndConfig(user)
	fmt.Println(err)

	crmAppInfo := crmApp.ToAppInfo()

	link, err := generateVerificationLink(email, crmAppInfo)
	log.Info("link: %v | err: %v", link, err)
	if log.IfError(err) {
		return nil, fmt.Errorf("system failed to generate link, try again later")
	}

	shortLink := utils.ShortUrl(utils.ShortUrlModel{
		LongUrl: link,
		Tags:    []string{"auth-link"},
		Length:  16,
	})

	if shortLink != "" {
		link = shortLink
	}

	admin, err := a.repo.FetchAdmin(user.AdminId)
	log.IfError(err)

	messageDetail := map[string]interface{}{
		"type": "crm_auth",
		"attributes": map[string]interface{}{
			"app_name": strings.ToUpper(cast.ToString(admin["business_name"])),
			"auth_url": link,
		},
	}

	scheduleMessage := domain.ScheduleMessage{
		Title:         "Auth Link",
		Message:       fmt.Sprintf("*#UNIQ OTP* \nUntuk Login ke aplikasi *%s*, \nklik link berikut:\n%v \n\n_untuk keamanan, jangan berikan link ini kepada siapapun_", strings.ToUpper(cast.ToString(admin["business_name"])), link),
		TimeDeliver:   time.Now().Unix() * 1000,
		Media:         "whatsapp",
		Receiver:      contact,
		MessageDetail: utils.SimplyToJson(messageDetail),
	}

	//if environment is dev, no need to set messageDetail
	if os.Getenv("server") == "development" {
		// scheduleMessage.MessageDetail = ""
	}

	scheduleMessages := []domain.ScheduleMessage{scheduleMessage}

	for _, msg := range scheduleMessages {
		msgMap := cast.ToMap(msg)
		err = google.Publish("messaging-gateway-production", msgMap)
		if err != nil {
			a.repo.AddScheduleMessage(msg)
		}
	}

	waSender := "6281717172171" //6281717172171, 6282134886445
	if senderEnv := os.Getenv("WA_SENDER_AUTH"); senderEnv != "" {
		waSender = senderEnv
	}

	data := map[string]interface{}{
		"wa_sender": waSender,
	}

	return data, err
}

func generateVerificationLink(email string, appInfo domain.CrmAppInfo) (string, error) {
	ctxBack := context.Background()
	client, err := firebase.GetFirebaseApp().Auth(ctxBack)
	if err != nil {
		fmt.Printf("error getting Auth client: %v\n", err)
	}

	actionCodeSetting := &auth.ActionCodeSettings{
		HandleCodeInApp:    true,
		AndroidPackageName: appInfo.Android.PackageName,
		IOSBundleID:        appInfo.Ios.BundleID,
		AndroidInstallApp:  true,
		URL:                fmt.Sprintf("%s/auth/login?email=%s", appInfo.Web.Url, email),
		DynamicLinkDomain:  utils.ExtractBaseUrl(domain.FirebaseDynamicLink()),
	}

	log.Info("actionCodeSetting: %v", utils.SimplyToJson(actionCodeSetting))

	return client.EmailSignInLink(ctxBack, email, actionCodeSetting)
}

func (a *authUseCase) Logout(user domain.UserSession) error {
	//remove firebase token
	a.repo.RemoveFirebaseToken(user)
	return nil
}

func (a *authUseCase) SendOtp(otp *models.OtpRequestInput, user domain.UserSession) (*models.OtpRequestResponse, error) {
	//validation
	log.Info("request otp: %v (%v), user: %v", otp.Contact, otp.ContactType, utils.SimplyToJson(user))
	if otp.ContactType != "whatsapp" {
		return nil, fmt.Errorf("contact type not supported '%v'", otp.ContactType)
	}
	otp.Contact = strings.TrimSpace(otp.Contact)
	if !utils.IsNumber(otp.Contact) {
		log.Info("invalid contact: %v", otp.Contact)
		return nil, fmt.Errorf("invalid contact '%v'", otp.Contact)
	}

	logTime := log.TimeInit()
	if !a.rateLimiter.Allow(otp.Contact) {
		log.Info("rate limit exceeded for '%v', rateLimitSet: %v", otp.Contact, a.rateLimiter.GetTimeWindow())
		return nil, exception.WithCode{Code: 55, Message: "rate limit exceeded, try again in 15 minutes"}
	}
	logTime.AddLog("check limiter")

	member, err := a.repo.FetchMemberByContact(otp.Contact, otp.ContactType)
	if log.IfError(err) {
		return nil, err
	}
	logTime.AddLog("FetchMemberByContact")

	if member.MemberID == 0 {
		log.Info("requesting otp non member: %v", otp.Contact)
		// return nil, fmt.Errorf("contact not found")
	}

	//generate code
	numbs := generate.RandomNumber(6)

	//hash the number
	numbsHash, err := utils.HashPasswordWithCost(cast.ToString(numbs), 10)
	if log.IfError(err) {
		return nil, err
	}
	logTime.AddLog("HashPassword")

	//encrypt hashed number
	token := utils.Encrypt(numbsHash, models.KEY_OTP)

	//save to db
	expiredAt := time.Now().Add(time.Minute*3).Unix() * 1000
	id, err := a.repo.AddOtp(otp, token, expiredAt)
	if log.IfError(err) {
		return nil, err
	}
	log.Info("otp added, id: %v", id)

	appName := ""
	admin, err := a.repo.FetchAdmin(user.AdminId)
	if businessName := cast.ToString(admin["business_name"]); !log.IfError(err) && businessName != "" {
		appName = fmt.Sprintf("*%v*", businessName)
	}

	//send the otp
	msg := domain.ScheduleMessage{
		Title:       "OTP",
		Message:     fmt.Sprintf("*#UNIQ OTP* \nUntuk Login ke aplikasi %v, \ngunakan kode berikut:\n%v \n\n_untuk keamanan, jangan berikan kode ini ini kepada siapapun_", appName, numbs),
		TimeDeliver: time.Now().Unix() * 1000,
		Media:       otp.ContactType,
		Receiver:    otp.Contact,
	}
	err = google.Publish("messaging-gateway-production", cast.ToMap(msg))
	if err != nil {
		log.IfError(a.repo.AddScheduleMessage(msg))
	}

	logTime.AddLog("saving..")
	logTime.Print()
	return &models.OtpRequestResponse{
		ExpiredAt: expiredAt,
		Token:     token,
	}, nil
}

func (a *authUseCase) ValidateOtp(token string, code string, user domain.UserSession) (*models.AuthToken, error) {
	if token == "" {
		return nil, exception.WithCode{Code: 400, Message: "token is empty"}
	}

	//fetch the data
	otp, err := a.repo.FetchOtp(token)
	if log.IfError(err) {
		return nil, err
	}

	//validate the token
	if otp.AuthOTPID == 0 {
		log.Info("token not found: %v", token)
		return nil, fmt.Errorf("token not found or already expired")
	}

	if otp.DateExpired < time.Now().Unix()*1000 {
		return nil, fmt.Errorf("code expired at %v", otp.DateExpired)
	}

	//decrypt token
	tokenDec := utils.Decrypt(otp.Token, models.KEY_OTP)

	//check hash token if matches
	if !utils.CheckPasswordHash(code, tokenDec) {
		return nil, fmt.Errorf("invalid code")
	}

	//fetch member by contact
	member, err := a.repo.FetchMemberByContact(otp.Contact, otp.ContactType)
	if log.IfError(err) {
		return nil, err
	}

	var authToken *models.AuthToken

	//for non member (in case registration), return firebase token
	// if member.MemberID == 0 {
	// 	authToken, err = a.createFirebaseToken(otp.Contact)
	// } else {
	// 	// create authentication
	// 	authToken, err = a.createAuthUser(int64(member.MemberID), user.AdminId)
	// }
	authToken, err = a.createFirebaseToken(otp.Contact)

	if log.IfError(err) {
		return nil, err
	}

	//remove the token data from db, so it will no longer be able to use
	err = a.repo.RemoveAuthOtp(otp.AuthOTPID)
	log.IfError(err)

	//if member phone status not confirmed, do so
	if member.PhoneVerified == 0 && member.MemberID > 0 {
		err = a.repo.SetPhoneVerified(member.MemberID)
		log.IfError(err)
		member.PhoneVerified = 1
	}

	return authToken, nil
}

func (a *authUseCase) createFirebaseToken(phone string) (*models.AuthToken, error) {
	phone = "+" + utils.FormatPhoneNumber(phone)

	ctxBack := context.Background()
	client, err := firebase.GetFirebaseApp().Auth(ctxBack)
	if err != nil {
		fmt.Printf("error getting Auth client: %v\n", err)
		return nil, err
	}

	var userRecord *auth.UserRecord

	//get (if exist)
	userRecord, err = client.GetUserByPhoneNumber(ctxBack, phone)
	if err != nil {
		if !strings.HasPrefix(err.Error(), "cannot find user from phone number") {
			log.IfError(err)
		}
		// if firebaseErr, ok := err.(*internal.FirebaseError); ok {
		// 	log.IfError(fmt.Errorf("error code: %v", firebaseErr.Code))
		// }
	}

	if userRecord == nil {
		params := (&auth.UserToCreate{}).
			PhoneNumber(phone)

		userRecord, err = client.CreateUser(ctxBack, params)
		if err != nil {
			log.Info("create firebase user err: %v", err)
			return nil, err
		}
		log.Info("user %v created, uid: %v", phone, userRecord.UID)
	} else {
		log.Info("user %v exist, uid: %v", phone, userRecord.UID)
	}

	log.Info("custom claims: %v", userRecord.CustomClaims)
	token, err := client.CustomTokenWithClaims(ctxBack, userRecord.UID, map[string]interface{}{
		"phone_number":   utils.FormatPhoneNumber(phone),
		"phone_verified": true,
	})

	if log.IfError(err) {
		return nil, err
	}

	// token, err = signInWithCustomToken(token)
	// if log.IfError(err) {
	// 	return nil, err
	// }

	// _, err = client.VerifyIDToken(ctxBack, token)
	// log.IfError(err)

	return &models.AuthToken{
		Token: token,
		Type:  "firebase",
	}, nil
}

func signInWithCustomToken(token string) (string, error) {
	apiKey := os.Getenv("FIREBASE_CRM_KEY")
	if apiKey == "" {
		return "", fmt.Errorf("FIREBASE_CRM_KEY not defined")
	}

	httpReq := utils.HttpRequest{
		Url:    fmt.Sprintf(verifyCustomTokenURL, apiKey),
		Method: "POST",
		PostRequest: utils.PostRequest{
			Body: map[string]interface{}{
				"token":             token,
				"returnSecureToken": true,
			},
		},
	}

	resp, err := httpReq.ExecuteRequest()
	if err != nil {
		return "", err
	}
	var respBody struct {
		IDToken string `json:"idToken"`
	}
	if err := json.Unmarshal(resp, &respBody); err != nil {
		return "", err
	}
	return respBody.IDToken, err
}

func (a *authUseCase) createAuthUser(memberId, adminId int64) (*models.AuthResponse, error) {
	member, err := a.repo.FetchMemberByAdmin(memberId, adminId)
	if log.IfError(err) {
		return nil, err
	}

	if member.MemberID == 0 {
		log.Info("member not found: %v (%v)", memberId, adminId)
		return nil, fmt.Errorf("member not found")
	}

	auth := authJwt.InitJWTAuth()
	uid := utils.Encrypt(cast.ToString(adminId), utils.UID_PASSSERVER)
	mid := utils.Encrypt(cast.ToString(memberId), utils.UID_PASSSERVER)
	authToken := auth.GenerateToken("uid", uid, "mid", mid)

	// members["member_type"] = memberType["name"]
	//get member type
	memberType, err := db.Query("select p.name from members_type mt join products p on mt.product_fkid = p.product_id where type_id = ?", member.TypeFkID)
	log.IfError(err)

	member.Barcode = utils.Encrypt(cast.ToString(memberId), utils.KEY_MEMBER_BARCODE)
	member.BarcodeUrl = generate.QrCode(member.Barcode, 100) //fmt.Sprintf("https://chart.googleapis.com/chart?chs=100x100&cht=qr&chl=%s", member.Barcode)
	member.MemberType = cast.ToString(memberType["name"])

	return &models.AuthResponse{
		Token:  *authToken,
		Member: *member,
	}, nil
}
