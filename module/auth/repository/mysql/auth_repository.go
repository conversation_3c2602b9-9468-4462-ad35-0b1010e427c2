package mysql

import (
	"database/sql"
	"strings"
	"time"

	"gitlab.com/uniqdev/backend/api-membership/core/cast"
	db "gitlab.com/uniqdev/backend/api-membership/core/db"
	"gitlab.com/uniqdev/backend/api-membership/core/log"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/models"
)

type authRepository struct {
	db db.Repository
}

func NewMysqlAuthRepository(conn *sql.DB) domain.AuthRepository {
	return &authRepository{db.Repository{Conn: conn}}
}

func (a authRepository) FetchMemberEmailByPhone(phone string, user domain.UserSession) (string, error) {
	sql := `select email from members m 
	join members_detail md on md.member_fkid=m.member_id 
	where md.admin_fkid=? and m.phone=?`
	data, err := db.Query(sql, user.AdminId, phone)
	return cast.ToString(data["email"]), err
}

func (a authRepository) FetchMemberByContact(contact string, contactType string) (*models.MemberEntity, error) {
	sql := `select * from members where $type = ? `

	if contactType != "email" && contactType != "phone" {
		contactType = "phone"
	}
	sql = strings.Replace(sql, "$type", contactType, 1)
	var result models.MemberEntity
	err := a.db.Prepare(sql, contact).Get(&result)
	return &result, err
}

func (a authRepository) FetchMemberByAdmin(memberId int64, adminId int64) (*models.MemberWithDetail, error) {
	sql := `SELECT * from members m 
	join members_detail md on m.member_id=md.member_fkid
	where m.member_id= ? and md.admin_fkid = ? `

	var result models.MemberWithDetail
	err := a.db.Prepare(sql, memberId, adminId).Get(&result)
	return &result, err
}

func (a authRepository) AddScheduleMessage(data ...domain.ScheduleMessage) error {
	err := db.WithTransaction(func(tx db.Transaction) error {
		for _, row := range data {
			row.DataCreated = time.Now().Unix() * 1000
			tx.Insert("scheduled_message", row.ToMap())
		}
		return nil
	})
	return err
}

func (a authRepository) FetchAdmin(adminId int64) (map[string]interface{}, error) {
	sql := "select * from admin where admin_id = ?"
	return db.Query(sql, adminId)
}

func (a authRepository) RemoveFirebaseToken(user domain.UserSession) error {
	resp, err := db.Update("members_detail", map[string]interface{}{
		"firebase_token": "",
	}, "member_fkid=? and admin_fkid = ?", user.MemberId, user.AdminId)

	eff, _ := resp.RowsAffected()
	log.Info("removing firebase token : %v (%v) | effected: %v", user.MemberId, user.AdminId, eff)
	return err
}

func (a authRepository) AddOtp(otp *models.OtpRequestInput, token string, expiredAt int64) (int64, error) {
	resp, err := db.Insert("auth_otp", map[string]interface{}{
		"contact":      otp.Contact,
		"contact_type": otp.ContactType,
		"token":        token,
		"date_created": time.Now().Unix() * 1000,
		"date_expired": expiredAt,
	})
	if err != nil {
		return 0, err
	}
	id, err := resp.LastInsertId()
	return id, err
}

func (a authRepository) FetchOtp(token string) (*models.AuthOTPEntity, error) {
	var result models.AuthOTPEntity
	err := a.db.Prepare("select * from auth_otp where token = ?", token).Get(&result)
	return &result, err
}

func (a authRepository) RemoveAuthOtp(id int64) error {
	_, err := db.Delete("auth_otp", "auth_otp_id = ?", id)
	return err
}

func (ar *authRepository) SetPhoneVerified(memberID int) error {
	_, err := db.Update("members", map[string]interface{}{
		"phone_verified": 1,
	}, "member_id = ?", memberID)
	return err
}
