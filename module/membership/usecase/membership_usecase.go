package usecase

import (
	"gitlab.com/uniqdev/backend/api-membership/domain"
	"gitlab.com/uniqdev/backend/api-membership/module/membership"
)

type membershipUseCase struct {
	repo membership.MembershipRepository
}

func NewMembershipUseCase(repository membership.MembershipRepository) membership.MembershipUseCase {
	return &membershipUseCase{repository}
}

func (m membershipUseCase) FetchMemberTypes(user domain.UserSession) ([]map[string]interface{}, error) {
	return m.repo.FetchMemberTypes(user)
}
