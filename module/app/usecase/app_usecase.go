package usecase

import (
	"encoding/json"
	"fmt"
	"os"

	"gitlab.com/uniqdev/backend/api-membership/core/log"
	domain "gitlab.com/uniqdev/backend/api-membership/domain"
)

type appUseCase struct {
	repo domain.AppRepository
}

func NewAppUseCase(repository domain.AppRepository) domain.AppUseCase {
	return &appUseCase{repository}
}

func (a appUseCase) FetchAppConfig(user domain.UserSession) (map[string]interface{}, error) {
	result := map[string]interface{}{
		"asset": map[string]interface{}{
			"toolbar_background":      "https://storage.googleapis.com/uniq-187911.appspot.com/staging/crm-assets/10/toolbar_back_heigh.png",
			"app_background":          "https://storage.googleapis.com/uniq-187911.appspot.com/staging/crm-assets/10/app_background.png",
			"profile_card_background": "https://storage.googleapis.com/uniq-187911.appspot.com/staging/crm-assets/10/background_profile_card.png",
			"app_icon":                "https://storage.googleapis.com/uniq-187911.appspot.com/staging/crm-assets/10/app_icon.png",
			"color": map[string]interface{}{
				"primary":      "#8C1115",
				"primary_dark": "#600207",
				"accent":       "#D81B60",
				"secondary":    "#f6ce2b",
			},
		},
		"language": map[string]interface{}{
			"point": "Baby Panda",
		},
	}

	appCrm, err := a.repo.FetchAppInfoAndConfig(user)
	if err != nil {
		return nil, err
	}

	if appCrm.CrmAppID == 0 || appCrm.AppConfig == "" || appCrm.AppInfo == "" {
		return result, nil
	}

	fmt.Println(appCrm)

	var appConfigMap map[string]interface{}
	log.IfError(json.Unmarshal([]byte(appCrm.AppConfig), &appConfigMap))

	var appInfoMap map[string]interface{}
	log.IfError(json.Unmarshal([]byte(appCrm.AppInfo), &appInfoMap))

	appInfoMap["dynamic_link"] = os.Getenv("FIREBASE_DYNAMIC_LINK")

	result = appConfigMap
	result["app_info"] = appInfoMap
	result["term_condition"] = appCrm.TermCondition
	result["privacy_policy"] = appCrm.PrivacyPolicy

	return result, nil
}

func (a appUseCase) FetchFAQs(user domain.UserSession) ([]map[string]interface{}, error) {
	return a.repo.FetchFAQs(int(user.AdminId))
}
